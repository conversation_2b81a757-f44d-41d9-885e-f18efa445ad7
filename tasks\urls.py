from django.urls import path
from . import views

urlpatterns = [
    # Task URLs
    path('', views.task_list, name='task_list'),
    path('create/', views.task_create, name='task_create'),
    path('<int:pk>/', views.task_detail, name='task_detail'),
    path('<int:pk>/update/', views.task_update, name='task_update'),
    path('<int:pk>/delete/', views.task_delete, name='task_delete'),

    # Timeline URL
    path('timeline/', views.task_timeline, name='task_timeline'),

    # Task Step URLs
    path('steps/<int:step_id>/toggle/', views.toggle_step, name='toggle_step'),

    # AJAX URLs
    path('ajax/task/<int:task_id>/',
         views.get_task_details, name='get_task_details'),
    path('ajax/task/<int:task_id>/update/',
         views.update_task_ajax, name='update_task_ajax'),

    # Category URLs
    path('categories/', views.category_list, name='category_list'),
    path('categories/create/', views.category_create, name='category_create'),
    path('categories/<int:pk>/update/',
         views.category_update, name='category_update'),
    path('categories/<int:pk>/delete/',
         views.category_delete, name='category_delete'),
]
