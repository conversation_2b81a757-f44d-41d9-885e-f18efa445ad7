{% extends 'tasks/base.html' %}

{% block title %}Profile - {{ user.username }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <!-- User Info Card -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-user-circle me-2"></i>
                        {{ user.username }}
                    </h4>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-user-circle fa-5x text-primary"></i>
                    </div>
                    
                    <h5>{{ user.get_full_name|default:user.username }}</h5>
                    
                    {% if user.email %}
                    <p class="text-muted">
                        <i class="fas fa-envelope me-1"></i>
                        {{ user.email }}
                    </p>
                    {% endif %}
                    
                    <p class="text-muted">
                        <i class="fas fa-calendar-alt me-1"></i>
                        Member since {{ user.date_joined|date:"M Y" }}
                    </p>
                    
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" disabled>
                            <i class="fas fa-edit me-2"></i>
                            Edit Profile
                        </button>
                        <button class="btn btn-outline-secondary" disabled>
                            <i class="fas fa-cog me-2"></i>
                            Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Statistics Cards -->
        <div class="col-md-8">
            <div class="row mb-4">
                <div class="col-md-6 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-tasks fa-2x text-primary mb-2"></i>
                            <h3 class="mb-0">{{ total_tasks }}</h3>
                            <p class="text-muted mb-0">Total Tasks</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h3 class="mb-0">{{ completed_tasks }}</h3>
                            <p class="text-muted mb-0">Completed</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                            <h3 class="mb-0">{{ pending_tasks }}</h3>
                            <p class="text-muted mb-0">Pending</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-play-circle fa-2x text-info mb-2"></i>
                            <h3 class="mb-0">{{ in_progress_tasks }}</h3>
                            <p class="text-muted mb-0">In Progress</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Completion Rate -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Completion Rate
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Overall Progress</span>
                        <span class="fw-bold">{{ completion_rate }}%</span>
                    </div>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: {{ completion_rate }}%;" 
                             aria-valuenow="{{ completion_rate }}" 
                             aria-valuemin="0" 
                             aria-valuemax="100">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Notifications Info -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bell me-2"></i>
                        Email Notifications
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary">{{ total_notifications }}</h4>
                            <p class="text-muted mb-0">Total Notifications</p>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ active_notifications }}</h4>
                            <p class="text-muted mb-0">Active Notifications</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Tasks -->
    {% if recent_tasks %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        Recent Tasks
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        {% for task in recent_tasks %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">
                                    <a href="{% url 'task_detail' task.pk %}" class="text-decoration-none">
                                        {{ task.title }}
                                    </a>
                                </h6>
                                <small class="text-muted">
                                    Updated {{ task.updated_at|timesince }} ago
                                </small>
                            </div>
                            <span class="badge bg-{{ task.status|default:'secondary' }} rounded-pill">
                                {{ task.get_status_display }}
                            </span>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="{% url 'task_list' %}" class="btn btn-outline-primary">
                            <i class="fas fa-list me-2"></i>
                            View All Tasks
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<style>
    .card {
        transition: transform 0.2s ease;
    }
    
    .card:hover {
        transform: translateY(-2px);
    }
    
    .progress {
        border-radius: 10px;
        background: var(--bg-tertiary);
    }
    
    .progress-bar {
        border-radius: 10px;
    }
    
    .list-group-item {
        background: transparent;
        border-color: var(--border-color);
        color: var(--text-primary);
    }
    
    .badge.bg-pending { background-color: var(--accent-yellow) !important; }
    .badge.bg-in_progress { background-color: var(--accent-blue) !important; }
    .badge.bg-completed { background-color: var(--accent-green) !important; }
    .badge.bg-cancelled { background-color: var(--accent-red) !important; }
</style>
{% endblock %}
