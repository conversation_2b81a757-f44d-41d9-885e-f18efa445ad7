#!/usr/bin/env python
"""
Test script for email notifications
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'task_manager_project.settings')
django.setup()

from tasks.models import Task, TaskNotification, Category
from tasks.notifications import create_default_notifications, send_task_created_notifications
from django.utils import timezone
from datetime import timedelta

def test_notification_system():
    print("Testing Task Manager Notification System")
    print("=" * 50)
    
    # Create a test category
    category, created = Category.objects.get_or_create(
        name="Test Category",
        defaults={'description': 'Category for testing notifications'}
    )
    if created:
        print(f"✓ Created test category: {category.name}")
    else:
        print(f"✓ Using existing category: {category.name}")
    
    # Create a test task
    task = Task.objects.create(
        title="Test Notification Task",
        description="This is a test task to verify email notifications are working",
        category=category,
        status='pending',
        priority='medium',
        due_date=timezone.now() + timedelta(hours=23)  # Due in 23 hours (should trigger 1-day reminder)
    )
    print(f"✓ Created test task: {task.title}")
    
    # Create default notifications
    test_email = "<EMAIL>"
    notifications = create_default_notifications(task, test_email)
    print(f"✓ Created {len(notifications)} default notifications for {test_email}")
    
    # Create additional notification types
    additional_notifications = [
        {
            'notification_type': 'status_change',
            'schedule': 'immediate',
        },
        {
            'notification_type': 'task_created',
            'schedule': 'immediate',
        }
    ]
    
    for notif_data in additional_notifications:
        notification, created = TaskNotification.objects.get_or_create(
            task=task,
            notification_type=notif_data['notification_type'],
            recipient_email=test_email,
            defaults={
                'schedule': notif_data['schedule'],
                'is_active': True,
            }
        )
        if created:
            print(f"✓ Created {notification.get_notification_type_display()} notification")
    
    # Test sending notifications
    print("\nTesting notification sending:")
    print("-" * 30)
    
    # Test task created notification
    sent_count = send_task_created_notifications(task)
    print(f"✓ Sent {sent_count} task created notifications")
    
    # Test due date notification (should send since task is due in 23 hours)
    due_notifications = TaskNotification.objects.filter(
        task=task,
        notification_type='due_date',
        is_active=True
    )
    
    sent_count = 0
    for notification in due_notifications:
        if notification.should_send_notification():
            if notification.send_notification():
                sent_count += 1
                print(f"✓ Sent due date reminder to {notification.recipient_email}")
        else:
            print(f"ℹ Due date reminder not yet due for {notification.recipient_email}")
    
    # Display all notifications for the task
    print(f"\nAll notifications for task '{task.title}':")
    print("-" * 50)
    all_notifications = TaskNotification.objects.filter(task=task)
    for notification in all_notifications:
        status = "✓ Active" if notification.is_active else "✗ Inactive"
        last_sent = notification.last_sent.strftime('%Y-%m-%d %H:%M') if notification.last_sent else "Never"
        print(f"{status} | {notification.get_notification_type_display()} | {notification.recipient_email} | {notification.get_schedule_display()} | Last sent: {last_sent}")
    
    print(f"\n✓ Test completed! Check your console for email output.")
    print(f"✓ Task ID: {task.id} - You can view it at: http://localhost:8000/{task.id}/")
    
    return task

if __name__ == "__main__":
    try:
        test_task = test_notification_system()
        print(f"\nTest task created with ID: {test_task.id}")
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()
