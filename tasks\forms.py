from django import forms
from django.forms import inlineformset_factory
from .models import Task, Category, TaskStep, TaskNotification


class CategoryForm(forms.ModelForm):
    class Meta:
        model = Category
        fields = ['name', 'description', 'color']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter category name'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Enter category description'
            }),
            'color': forms.TextInput(attrs={
                'class': 'form-control',
                'type': 'color',
                'placeholder': '#007bff'
            }),
        }


class TaskForm(forms.ModelForm):
    class Meta:
        model = Task
        fields = ['title', 'description', 'category',
                  'status', 'priority', 'due_date']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter task title'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Enter task description'
            }),
            'category': forms.Select(attrs={
                'class': 'form-control'
            }),
            'status': forms.Select(attrs={
                'class': 'form-control'
            }),
            'priority': forms.Select(attrs={
                'class': 'form-control'
            }),
            'due_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
        }


class TaskStepForm(forms.ModelForm):
    class Meta:
        model = TaskStep
        fields = ['title', 'description', 'is_completed', 'order']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter step title'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'Enter step description (optional)'
            }),
            'is_completed': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'order': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0'
            }),
        }


# Create formset for task steps
TaskStepFormSet = inlineformset_factory(
    Task,
    TaskStep,
    form=TaskStepForm,
    extra=1,
    can_delete=True,
    fields=['title', 'description', 'is_completed', 'order']
)


class TaskNotificationForm(forms.ModelForm):
    class Meta:
        model = TaskNotification
        fields = ['notification_type', 'recipient_email', 'schedule', 'is_active']
        widgets = {
            'notification_type': forms.Select(attrs={
                'class': 'form-control'
            }),
            'recipient_email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter email address'
            }),
            'schedule': forms.Select(attrs={
                'class': 'form-control'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }


# Create formset for task notifications
TaskNotificationFormSet = inlineformset_factory(
    Task,
    TaskNotification,
    form=TaskNotificationForm,
    extra=1,
    can_delete=True,
    fields=['notification_type', 'recipient_email', 'schedule', 'is_active']
)


class TaskFormWithNotifications(forms.ModelForm):
    """Extended task form that includes notification email field"""
    notification_email = forms.EmailField(
        required=False,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter email for notifications (optional)'
        }),
        help_text='If provided, default notifications will be created for this task'
    )

    class Meta:
        model = Task
        fields = ['title', 'description', 'category', 'status', 'priority', 'due_date']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter task title'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Enter task description'
            }),
            'category': forms.Select(attrs={
                'class': 'form-control'
            }),
            'status': forms.Select(attrs={
                'class': 'form-control'
            }),
            'priority': forms.Select(attrs={
                'class': 'form-control'
            }),
            'due_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
        }

    def save(self, commit=True):
        task = super().save(commit)

        # Create default notifications if email is provided
        if commit and self.cleaned_data.get('notification_email'):
            from .notifications import create_default_notifications
            create_default_notifications(task, self.cleaned_data['notification_email'])

        return task
