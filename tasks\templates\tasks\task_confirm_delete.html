{% extends 'tasks/base.html' %}

{% block title %}Delete Task - Task Manager{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-6 col-md-8">
        <div class="card fade-in-up">
            <div class="card-header bg-danger text-white">
                <h4 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Delete
                </h4>
            </div>
            <div class="card-body text-center">
                <div class="mb-4">
                    <i class="fas fa-trash fa-3x text-danger mb-3"></i>
                    <h5>Are you sure you want to delete this task?</h5>
                    <p class="text-muted">This action cannot be undone.</p>
                </div>
                
                <div class="p-3 rounded mb-4" style="background-color: var(--bg-tertiary); border-left: 4px solid var(--accent-red);">
                    <h6 class="mb-2">{{ task.title }}</h6>
                    {% if task.description %}
                        <p class="text-muted mb-0">{{ task.description|truncatewords:20 }}</p>
                    {% endif %}
                    <small class="text-muted">
                        Created: {{ task.created_at|date:"M d, Y" }}
                    </small>
                </div>
                
                <form method="POST" class="d-inline">
                    {% csrf_token %}
                    <div class="d-flex justify-content-center gap-3">
                        <a href="{% url 'task_detail' task.pk %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>Delete Task
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add confirmation animation
        const deleteForm = document.querySelector('form');
        const deleteBtn = deleteForm.querySelector('button[type="submit"]');
        
        deleteForm.addEventListener('submit', function(e) {
            deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Deleting...';
            deleteBtn.disabled = true;
        });
    });
</script>
{% endblock %}
