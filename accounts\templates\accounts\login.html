{% extends 'tasks/base.html' %}

{% block title %}Sign In - Task Manager{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card">
                <div class="card-header text-center">
                    <h3 class="mb-0">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        Welcome Back
                    </h3>
                    <p class="text-muted mb-0">Sign in to your account</p>
                </div>
                <div class="card-body p-4">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">
                                <i class="fas fa-user me-1"></i>Username
                            </label>
                            {{ form.username }}
                            {% if form.username.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.username.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.password.id_for_label }}" class="form-label">
                                <i class="fas fa-lock me-1"></i>Password
                            </label>
                            {{ form.password }}
                            {% if form.password.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.password.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                            <label class="form-check-label" for="remember_me">
                                Remember me
                            </label>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Sign In
                            </button>
                        </div>
                    </form>

                    <hr class="my-4">

                    <div class="text-center">
                        <p class="mb-0">Don't have an account?</p>
                        <a href="{% url 'signup' %}" class="btn btn-outline-primary mt-2">
                            <i class="fas fa-user-plus me-2"></i>
                            Create Account
                        </a>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <small class="text-muted">
                    <i class="fas fa-shield-alt me-1"></i>
                    Your data is secure and private
                </small>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        border: 1px solid var(--border-color);
    }
    
    .card-header {
        background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
        color: white;
        border-bottom: none;
    }
    
    .form-control {
        background: rgba(113, 128, 150, 0.8);
        border: 1px solid var(--border-color);
        color: var(--text-primary);
        padding: 12px 16px;
        font-size: 16px;
    }
    
    .form-control:focus {
        background: rgba(113, 128, 150, 1);
        border-color: var(--accent-blue);
        box-shadow: 0 0 0 0.2rem rgba(66, 153, 225, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
        border: none;
        padding: 12px 24px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(66, 153, 225, 0.3);
    }
    
    .btn-outline-primary {
        border-color: var(--accent-blue);
        color: var(--accent-blue);
    }
    
    .btn-outline-primary:hover {
        background: var(--accent-blue);
        border-color: var(--accent-blue);
    }
</style>
{% endblock %}
