"""
Task notification utilities and functions
"""
from django.utils import timezone
from django.db.models import Q
from .models import Task, TaskNotification
import logging

logger = logging.getLogger(__name__)


def create_default_notifications(task, recipient_email):
    """
    Create default notifications for a new task
    """
    default_notifications = [
        {
            'notification_type': 'due_date',
            'schedule': '1_day',
        },
        {
            'notification_type': 'overdue',
            'schedule': 'daily',
        },
    ]
    
    created_notifications = []
    for notification_data in default_notifications:
        notification, created = TaskNotification.objects.get_or_create(
            task=task,
            notification_type=notification_data['notification_type'],
            recipient_email=recipient_email,
            defaults={
                'schedule': notification_data['schedule'],
                'is_active': True,
            }
        )
        if created:
            created_notifications.append(notification)
    
    return created_notifications


def send_status_change_notifications(task, old_status=None):
    """
    Send notifications when task status changes
    """
    notifications = TaskNotification.objects.filter(
        task=task,
        notification_type='status_change',
        is_active=True
    )
    
    sent_count = 0
    for notification in notifications:
        try:
            if notification.send_notification():
                sent_count += 1
                logger.info(f"Sent status change notification for task {task.id} to {notification.recipient_email}")
        except Exception as e:
            logger.error(f"Failed to send status change notification for task {task.id}: {e}")
    
    return sent_count


def send_step_completed_notifications(task_step):
    """
    Send notifications when a task step is completed
    """
    task = task_step.task
    notifications = TaskNotification.objects.filter(
        task=task,
        notification_type='step_completed',
        is_active=True
    )
    
    sent_count = 0
    for notification in notifications:
        try:
            if notification.send_notification():
                sent_count += 1
                logger.info(f"Sent step completed notification for task {task.id} to {notification.recipient_email}")
        except Exception as e:
            logger.error(f"Failed to send step completed notification for task {task.id}: {e}")
    
    return sent_count


def send_task_created_notifications(task):
    """
    Send notifications when a new task is created
    """
    notifications = TaskNotification.objects.filter(
        task=task,
        notification_type='task_created',
        is_active=True
    )
    
    sent_count = 0
    for notification in notifications:
        try:
            if notification.send_notification():
                sent_count += 1
                logger.info(f"Sent task created notification for task {task.id} to {notification.recipient_email}")
        except Exception as e:
            logger.error(f"Failed to send task created notification for task {task.id}: {e}")
    
    return sent_count


def process_scheduled_notifications():
    """
    Process and send all scheduled notifications that are due
    """
    now = timezone.now()
    
    # Get all active notifications
    notifications = TaskNotification.objects.filter(
        is_active=True
    ).select_related('task')
    
    sent_count = 0
    error_count = 0
    
    for notification in notifications:
        try:
            if notification.should_send_notification():
                if notification.send_notification():
                    sent_count += 1
                    logger.info(f"Sent scheduled notification {notification.id}")
                else:
                    error_count += 1
                    logger.warning(f"Failed to send scheduled notification {notification.id}")
        except Exception as e:
            error_count += 1
            logger.error(f"Error processing notification {notification.id}: {e}")
    
    logger.info(f"Processed scheduled notifications: {sent_count} sent, {error_count} errors")
    return sent_count, error_count


def get_due_soon_tasks(hours=24):
    """
    Get tasks that are due within the specified number of hours
    """
    now = timezone.now()
    due_threshold = now + timezone.timedelta(hours=hours)
    
    return Task.objects.filter(
        due_date__isnull=False,
        due_date__lte=due_threshold,
        due_date__gte=now,
        status__in=['pending', 'in_progress']
    )


def get_overdue_tasks():
    """
    Get all overdue tasks
    """
    now = timezone.now()
    
    return Task.objects.filter(
        due_date__isnull=False,
        due_date__lt=now,
        status__in=['pending', 'in_progress']
    )


def send_daily_digest(recipient_email):
    """
    Send a daily digest email with task summary
    """
    from django.template.loader import render_to_string
    from django.core.mail import send_mail
    from django.conf import settings
    
    # Get tasks for digest
    overdue_tasks = get_overdue_tasks()
    due_today_tasks = get_due_soon_tasks(24)
    
    if not overdue_tasks.exists() and not due_today_tasks.exists():
        return False  # No tasks to report
    
    try:
        # Render email content
        subject = f"Daily Task Digest - {timezone.now().strftime('%B %d, %Y')}"
        
        context = {
            'overdue_tasks': overdue_tasks,
            'due_today_tasks': due_today_tasks,
            'recipient_email': recipient_email,
            'date': timezone.now().date(),
        }
        
        # For now, use a simple text message
        # You can create a proper template later
        message = f"""
Daily Task Digest - {timezone.now().strftime('%B %d, %Y')}

Overdue Tasks ({overdue_tasks.count()}):
"""
        for task in overdue_tasks:
            message += f"- {task.title} (Due: {task.due_date.strftime('%m/%d/%Y')})\n"
        
        message += f"\nDue Today ({due_today_tasks.count()}):\n"
        for task in due_today_tasks:
            message += f"- {task.title} (Due: {task.due_date.strftime('%m/%d/%Y %H:%M')})\n"
        
        message += "\nView all tasks: http://localhost:8000/\n"
        
        send_mail(
            subject=subject,
            message=message,
            from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
            recipient_list=[recipient_email],
            fail_silently=False,
        )
        
        logger.info(f"Sent daily digest to {recipient_email}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to send daily digest to {recipient_email}: {e}")
        return False


def cleanup_old_notifications(days=30):
    """
    Clean up old notification records
    """
    cutoff_date = timezone.now() - timezone.timedelta(days=days)
    
    deleted_count = TaskNotification.objects.filter(
        last_sent__lt=cutoff_date,
        is_active=False
    ).delete()[0]
    
    logger.info(f"Cleaned up {deleted_count} old notification records")
    return deleted_count
