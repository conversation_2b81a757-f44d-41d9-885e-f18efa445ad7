"""
Django management command to send scheduled task notifications
"""
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from tasks.notifications import (
    process_scheduled_notifications,
    send_daily_digest,
    cleanup_old_notifications,
    get_due_soon_tasks,
    get_overdue_tasks
)
from tasks.models import TaskNotification
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Send scheduled task notifications'

    def add_arguments(self, parser):
        parser.add_argument(
            '--type',
            type=str,
            choices=['all', 'due', 'overdue', 'digest', 'cleanup'],
            default='all',
            help='Type of notifications to send (default: all)'
        )
        
        parser.add_argument(
            '--email',
            type=str,
            help='Send digest to specific email address'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be sent without actually sending'
        )
        
        parser.add_argument(
            '--cleanup-days',
            type=int,
            default=30,
            help='Days to keep old notification records (default: 30)'
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS(
                f'Starting notification processing at {timezone.now()}'
            )
        )
        
        notification_type = options['type']
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No emails will be sent')
            )
        
        try:
            if notification_type in ['all', 'due', 'overdue']:
                self.process_scheduled_notifications(dry_run)
            
            if notification_type in ['all', 'digest']:
                self.process_daily_digest(options.get('email'), dry_run)
            
            if notification_type in ['all', 'cleanup']:
                self.cleanup_notifications(options['cleanup_days'], dry_run)
                
        except Exception as e:
            raise CommandError(f'Error processing notifications: {e}')
        
        self.stdout.write(
            self.style.SUCCESS('Notification processing completed')
        )

    def process_scheduled_notifications(self, dry_run=False):
        """Process and send scheduled notifications"""
        self.stdout.write('Processing scheduled notifications...')
        
        if dry_run:
            # Show what would be sent
            due_tasks = get_due_soon_tasks(24)
            overdue_tasks = get_overdue_tasks()
            
            self.stdout.write(f'Tasks due soon: {due_tasks.count()}')
            for task in due_tasks:
                notifications = TaskNotification.objects.filter(
                    task=task,
                    notification_type='due_date',
                    is_active=True
                )
                for notification in notifications:
                    if notification.should_send_notification():
                        self.stdout.write(
                            f'  Would send due date reminder for "{task.title}" to {notification.recipient_email}'
                        )
            
            self.stdout.write(f'Overdue tasks: {overdue_tasks.count()}')
            for task in overdue_tasks:
                notifications = TaskNotification.objects.filter(
                    task=task,
                    notification_type='overdue',
                    is_active=True
                )
                for notification in notifications:
                    if notification.should_send_notification():
                        self.stdout.write(
                            f'  Would send overdue alert for "{task.title}" to {notification.recipient_email}'
                        )
        else:
            # Actually send notifications
            sent_count, error_count = process_scheduled_notifications()
            self.stdout.write(
                self.style.SUCCESS(
                    f'Sent {sent_count} notifications, {error_count} errors'
                )
            )

    def process_daily_digest(self, email=None, dry_run=False):
        """Process daily digest emails"""
        self.stdout.write('Processing daily digest...')
        
        if email:
            # Send to specific email
            if dry_run:
                due_tasks = get_due_soon_tasks(24)
                overdue_tasks = get_overdue_tasks()
                self.stdout.write(
                    f'Would send daily digest to {email} with {overdue_tasks.count()} overdue and {due_tasks.count()} due tasks'
                )
            else:
                success = send_daily_digest(email)
                if success:
                    self.stdout.write(
                        self.style.SUCCESS(f'Sent daily digest to {email}')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'No tasks to report for {email}')
                    )
        else:
            # Send to all users with digest notifications
            digest_notifications = TaskNotification.objects.filter(
                notification_type__in=['daily', 'weekly'],
                is_active=True
            ).values_list('recipient_email', flat=True).distinct()
            
            sent_count = 0
            for recipient_email in digest_notifications:
                if dry_run:
                    due_tasks = get_due_soon_tasks(24)
                    overdue_tasks = get_overdue_tasks()
                    self.stdout.write(
                        f'Would send daily digest to {recipient_email} with {overdue_tasks.count()} overdue and {due_tasks.count()} due tasks'
                    )
                else:
                    if send_daily_digest(recipient_email):
                        sent_count += 1
            
            if not dry_run:
                self.stdout.write(
                    self.style.SUCCESS(f'Sent {sent_count} daily digest emails')
                )

    def cleanup_notifications(self, days, dry_run=False):
        """Clean up old notification records"""
        self.stdout.write(f'Cleaning up notifications older than {days} days...')
        
        if dry_run:
            cutoff_date = timezone.now() - timezone.timedelta(days=days)
            count = TaskNotification.objects.filter(
                last_sent__lt=cutoff_date,
                is_active=False
            ).count()
            self.stdout.write(f'Would delete {count} old notification records')
        else:
            deleted_count = cleanup_old_notifications(days)
            self.stdout.write(
                self.style.SUCCESS(f'Deleted {deleted_count} old notification records')
            )
