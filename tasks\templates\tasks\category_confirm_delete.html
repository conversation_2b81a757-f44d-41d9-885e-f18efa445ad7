{% extends 'tasks/base.html' %}

{% block title %}Delete Category - Task Manager{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-6 col-md-8">
        <div class="card fade-in-up">
            <div class="card-header bg-danger text-white">
                <h4 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Delete
                </h4>
            </div>
            <div class="card-body text-center">
                <div class="mb-4">
                    <i class="fas fa-trash fa-3x text-danger mb-3"></i>
                    <h5>Are you sure you want to delete this category?</h5>
                    <p class="text-muted">This action cannot be undone. Tasks in this category will not be deleted but will become uncategorized.</p>
                </div>
                
                <div class="p-3 rounded mb-4" style="background-color: var(--bg-tertiary); border-left: 4px solid var(--accent-red);">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="category-color-indicator me-2" 
                             style="background-color: {{ category.color }}; width: 20px; height: 20px; border-radius: 50%;"></div>
                        <h6 class="mb-0">{{ category.name }}</h6>
                    </div>
                    {% if category.description %}
                        <p class="text-muted mb-2">{{ category.description|truncatewords:20 }}</p>
                    {% endif %}
                    <small class="text-muted">
                        <i class="fas fa-tasks me-1"></i>{{ category.tasks.count }} task{{ category.tasks.count|pluralize }} in this category
                    </small>
                </div>
                
                <form method="POST" class="d-inline">
                    {% csrf_token %}
                    <div class="d-flex justify-content-center gap-3">
                        <a href="{% url 'category_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>Delete Category
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add confirmation animation
        const deleteForm = document.querySelector('form');
        const deleteBtn = deleteForm.querySelector('button[type="submit"]');
        
        deleteForm.addEventListener('submit', function(e) {
            deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Deleting...';
            deleteBtn.disabled = true;
        });
    });
</script>
{% endblock %}
