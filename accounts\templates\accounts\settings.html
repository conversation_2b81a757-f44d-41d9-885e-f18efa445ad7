{% extends 'tasks/base.html' %}

{% block title %}Settings - {{ user.username }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10 col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        Account Settings
                    </h4>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Notification Settings -->
                        <div class="settings-section">
                            <h5 class="section-title">
                                <i class="fas fa-bell me-2"></i>
                                Notification Preferences
                            </h5>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    {{ form.email_notifications }}
                                    <label class="form-check-label" for="{{ form.email_notifications.id_for_label }}">
                                        {{ form.email_notifications.label }}
                                    </label>
                                </div>
                                <small class="form-text text-muted">
                                    Receive email notifications for task reminders, due dates, and status changes.
                                </small>
                            </div>
                        </div>

                        <!-- Task Settings -->
                        <div class="settings-section">
                            <h5 class="section-title">
                                <i class="fas fa-tasks me-2"></i>
                                Task Preferences
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.default_task_priority.id_for_label }}" class="form-label">
                                        {{ form.default_task_priority.label }}
                                    </label>
                                    {{ form.default_task_priority }}
                                    <small class="form-text text-muted">
                                        Default priority for new tasks.
                                    </small>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.tasks_per_page.id_for_label }}" class="form-label">
                                        {{ form.tasks_per_page.label }}
                                    </label>
                                    {{ form.tasks_per_page }}
                                    <small class="form-text text-muted">
                                        Number of tasks to display per page.
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Timeline Settings -->
                        <div class="settings-section">
                            <h5 class="section-title">
                                <i class="fas fa-calendar-alt me-2"></i>
                                Timeline Preferences
                            </h5>
                            
                            <div class="mb-3">
                                <label for="{{ form.default_timeline_view.id_for_label }}" class="form-label">
                                    {{ form.default_timeline_view.label }}
                                </label>
                                {{ form.default_timeline_view }}
                                <small class="form-text text-muted">
                                    Your preferred view when opening the timeline.
                                </small>
                            </div>
                        </div>

                        <!-- Appearance Settings -->
                        <div class="settings-section">
                            <h5 class="section-title">
                                <i class="fas fa-palette me-2"></i>
                                Appearance
                            </h5>
                            
                            <div class="mb-3">
                                <label for="{{ form.theme_preference.id_for_label }}" class="form-label">
                                    {{ form.theme_preference.label }}
                                </label>
                                {{ form.theme_preference }}
                                <small class="form-text text-muted">
                                    Choose your preferred theme. Auto will follow your system settings.
                                </small>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                Save Settings
                            </button>
                            <a href="{% url 'profile' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>
                                Back to Profile
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <a href="{% url 'edit_profile' %}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-user-edit me-2"></i>
                                Edit Profile
                            </a>
                        </div>
                        <div class="col-md-6 mb-2">
                            <a href="{% url 'change_password' %}" class="btn btn-outline-warning w-100">
                                <i class="fas fa-key me-2"></i>
                                Change Password
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .settings-section {
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid var(--border-color);
    }
    
    .settings-section:last-of-type {
        border-bottom: none;
        margin-bottom: 1rem;
    }
    
    .section-title {
        color: var(--accent-blue);
        margin-bottom: 1rem;
        font-weight: 600;
    }
    
    .form-control, .form-select {
        background: rgba(113, 128, 150, 0.8);
        border: 1px solid var(--border-color);
        color: var(--text-primary);
    }
    
    .form-control:focus, .form-select:focus {
        background: rgba(113, 128, 150, 1);
        border-color: var(--accent-green);
        box-shadow: 0 0 0 0.2rem rgba(72, 187, 120, 0.25);
    }
    
    .form-check-input {
        background-color: rgba(113, 128, 150, 0.8);
        border-color: var(--border-color);
    }
    
    .form-check-input:checked {
        background-color: var(--accent-green);
        border-color: var(--accent-green);
    }
    
    .btn-success {
        background: linear-gradient(135deg, var(--accent-green), var(--accent-blue));
        border: none;
    }
    
    .btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(72, 187, 120, 0.3);
    }
</style>
{% endblock %}
