# Generated by Django 5.2.3 on 2025-06-20 17:19

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tasks', '0003_taskstep'),
    ]

    operations = [
        migrations.CreateModel(
            name='TaskNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('due_date', 'Due Date Reminder'), ('overdue', 'Overdue Alert'), ('status_change', 'Status Change'), ('step_completed', 'Step Completed'), ('task_created', 'Task Created')], max_length=20)),
                ('recipient_email', models.EmailField(max_length=254)),
                ('schedule', models.CharField(choices=[('immediate', 'Immediate'), ('1_hour', '1 Hour Before'), ('2_hours', '2 Hours Before'), ('6_hours', '6 Hours Before'), ('12_hours', '12 Hours Before'), ('1_day', '1 Day Before'), ('2_days', '2 Days Before'), ('1_week', '1 Week Before'), ('daily', 'Daily Digest'), ('weekly', 'Weekly Digest')], default='immediate', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('last_sent', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='tasks.task')),
            ],
            options={
                'ordering': ['task', 'notification_type'],
                'unique_together': {('task', 'notification_type', 'recipient_email')},
            },
        ),
    ]
