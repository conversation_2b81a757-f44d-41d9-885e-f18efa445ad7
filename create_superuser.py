#!/usr/bin/env python
"""
Create a superuser for testing
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'task_manager_project.settings')
django.setup()

from django.contrib.auth.models import User

def create_superuser():
    username = 'admin'
    email = '<EMAIL>'
    password = 'admin123'
    
    if User.objects.filter(username=username).exists():
        print(f"User '{username}' already exists!")
        return
    
    user = User.objects.create_superuser(username=username, email=email, password=password)
    print(f"Superuser '{username}' created successfully!")
    print(f"Email: {email}")
    print(f"Password: {password}")

if __name__ == "__main__":
    create_superuser()
