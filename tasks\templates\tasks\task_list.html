{% extends 'tasks/base.html' %}

{% block title %}Task List - Task Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card fade-in-up">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-tasks me-2"></i>My Tasks
                </h4>
                <a href="{% url 'task_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>New Task
                </a>
            </div>
            <div class="card-body">
                <!-- Search and Filter Form -->
                <form method="GET" class="mb-4">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="search-container">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" name="search" class="form-control" 
                                       placeholder="Search tasks..." value="{{ search_query|default:'' }}">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select name="status" class="form-select">
                                <option value="">All Status</option>
                                {% for value, label in status_choices %}
                                    <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>
                                        {{ label }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select name="priority" class="form-select">
                                <option value="">All Priorities</option>
                                {% for value, label in priority_choices %}
                                    <option value="{{ value }}" {% if priority_filter == value %}selected{% endif %}>
                                        {{ label }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select name="category" class="form-select">
                                <option value="">All Categories</option>
                                {% for category in categories %}
                                    <option value="{{ category.id }}" {% if category_filter == category.id|stringformat:"s" %}selected{% endif %}>
                                        {{ category.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-filter me-1"></i>Filter
                            </button>
                        </div>
                    </div>
                </form>

                <!-- Tasks Grid -->
                {% if tasks %}
                    <div class="row">
                        {% for task in tasks %}
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="card h-100 task-card fade-in-up">
                                    <div class="card-body d-flex flex-column">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <h5 class="card-title mb-0">{{ task.title }}</h5>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                        type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-end dropdown-menu-dark">
                                                    <li><a class="dropdown-item" href="{% url 'task_detail' task.pk %}">
                                                        <i class="fas fa-eye me-2"></i>View
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="{% url 'task_update' task.pk %}">
                                                        <i class="fas fa-edit me-2"></i>Edit
                                                    </a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item text-danger" href="{% url 'task_delete' task.pk %}">
                                                        <i class="fas fa-trash me-2"></i>Delete
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        
                                        {% if task.description %}
                                            <p class="card-text text-muted mb-3">
                                                {{ task.description|truncatewords:15 }}
                                            </p>
                                        {% endif %}
                                        
                                        <div class="mt-auto">
                                            {% if task.category %}
                                                <div class="mb-2">
                                                    <span class="badge" style="background-color: {{ task.category.color }};">
                                                        <i class="fas fa-tag me-1"></i>{{ task.category.name }}
                                                    </span>
                                                </div>
                                            {% endif %}

                                            {% if task.has_steps %}
                                                <div class="mb-2">
                                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                                        <small class="text-muted">Progress</small>
                                                        <small class="text-muted">{{ task.completed_steps_count }}/{{ task.total_steps_count }}</small>
                                                    </div>
                                                    <div class="progress" style="height: 6px;">
                                                        <div class="progress-bar"
                                                             style="width: {{ task.progress_percentage }}%; background-color: {{ task.progress_color }};"
                                                             data-task-id="{{ task.pk }}">
                                                        </div>
                                                    </div>
                                                </div>
                                            {% endif %}

                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <span class="badge" style="background-color: {{ task.status_color }};">
                                                    {{ task.get_status_display }}
                                                </span>
                                                <span class="badge" style="background-color: {{ task.priority_color }};">
                                                    {{ task.get_priority_display }}
                                                </span>
                                            </div>
                                            
                                            {% if task.due_date %}
                                                <small class="text-muted d-block">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    Due: {{ task.due_date|date:"M d, Y H:i" }}
                                                    {% if task.is_overdue %}
                                                        <span class="text-danger ms-1">
                                                            <i class="fas fa-exclamation-triangle"></i> Overdue
                                                        </span>
                                                    {% endif %}
                                                </small>
                                            {% endif %}
                                            
                                            <small class="text-muted d-block mt-1">
                                                <i class="fas fa-clock me-1"></i>
                                                Created: {{ task.created_at|date:"M d, Y" }}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-rocket fa-3x text-primary mb-3"></i>
                        <h3 class="text-primary mb-3">Welcome to Your Personal Task Manager!</h3>
                        <p class="text-muted mb-4">
                            You're all set up! Start organizing your life by creating your first task.<br>
                            Track progress, set due dates, and get email notifications to stay on top of everything.
                        </p>

                        <div class="row justify-content-center mb-4">
                            <div class="col-md-8">
                                <div class="row text-center">
                                    <div class="col-md-4 mb-3">
                                        <i class="fas fa-plus-circle fa-2x text-success mb-2"></i>
                                        <h6>Create Tasks</h6>
                                        <small class="text-muted">Add tasks with descriptions, due dates, and priorities</small>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <i class="fas fa-bell fa-2x text-warning mb-2"></i>
                                        <h6>Get Notified</h6>
                                        <small class="text-muted">Receive email reminders and status updates</small>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <i class="fas fa-chart-line fa-2x text-info mb-2"></i>
                                        <h6>Track Progress</h6>
                                        <small class="text-muted">Break tasks into steps and monitor completion</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <a href="{% url 'task_create' %}" class="btn btn-primary btn-lg">
                            <i class="fas fa-plus me-2"></i>Create Your First Task
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Floating Action Button -->
<a href="{% url 'task_create' %}" class="floating-action-btn pulse" title="Create New Task">
    <i class="fas fa-plus"></i>
</a>
{% endblock %}

{% block extra_js %}
<script>
    // Add smooth animations to task cards
    document.addEventListener('DOMContentLoaded', function() {
        const taskCards = document.querySelectorAll('.task-card');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        taskCards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.6s ease';
            observer.observe(card);
        });

        // Enhanced dropdown z-index fix
        document.addEventListener('show.bs.dropdown', function (e) {
            const dropdown = e.target.closest('.dropdown');
            const menu = dropdown.querySelector('.dropdown-menu');
            if (menu) {
                menu.style.zIndex = '99999';
                menu.style.position = 'absolute';
            }
        });

        document.addEventListener('shown.bs.dropdown', function (e) {
            const dropdown = e.target.closest('.dropdown');
            const menu = dropdown.querySelector('.dropdown-menu');
            if (menu) {
                menu.style.zIndex = '99999';
                menu.style.position = 'absolute';
                // Force reposition
                menu.style.transform = 'translate3d(0, 0, 0)';
            }
        });

        document.addEventListener('hidden.bs.dropdown', function (e) {
            const dropdown = e.target.closest('.dropdown');
            const menu = dropdown.querySelector('.dropdown-menu');
            if (menu) {
                menu.style.zIndex = '';
                menu.style.transform = '';
            }
        });
    });
</script>
{% endblock %}
