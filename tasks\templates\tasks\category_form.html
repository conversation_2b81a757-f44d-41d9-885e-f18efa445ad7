{% extends 'tasks/base.html' %}

{% block title %}{{ title }} - Task Manager{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-6 col-md-8">
        <div class="card fade-in-up">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-{% if 'Create' in title %}plus{% else %}edit{% endif %} me-2"></i>
                    {{ title }}
                </h4>
            </div>
            <div class="card-body">
                <form method="POST" id="categoryForm">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">
                            <i class="fas fa-tag me-1"></i>Name *
                        </label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.name.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">
                            <i class="fas fa-align-left me-1"></i>Description
                        </label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.description.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-4">
                        <label for="{{ form.color.id_for_label }}" class="form-label">
                            <i class="fas fa-palette me-1"></i>Color
                        </label>
                        <div class="d-flex align-items-center">
                            {{ form.color }}
                            <div class="color-preview ms-3" id="colorPreview" 
                                 style="width: 40px; height: 40px; border-radius: 50%; border: 2px solid var(--border-color); background-color: {{ form.color.value|default:'#007bff' }};"></div>
                        </div>
                        {% if form.color.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.color.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">
                            Choose a color to represent this category.
                        </small>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'category_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            {% if 'Create' in title %}Create Category{% else %}Update Category{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Color picker preview
        const colorInput = document.querySelector('input[type="color"]');
        const colorPreview = document.getElementById('colorPreview');
        
        if (colorInput && colorPreview) {
            colorInput.addEventListener('input', function() {
                colorPreview.style.backgroundColor = this.value;
            });
        }
        
        // Add form validation feedback
        const form = document.getElementById('categoryForm');
        const inputs = form.querySelectorAll('input, textarea');
        
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.value.trim() !== '') {
                    this.classList.add('is-valid');
                    this.classList.remove('is-invalid');
                } else if (this.hasAttribute('required')) {
                    this.classList.add('is-invalid');
                    this.classList.remove('is-valid');
                }
            });
            
            input.addEventListener('input', function() {
                if (this.classList.contains('is-invalid') && this.value.trim() !== '') {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                }
            });
        });
        
        // Form submission animation
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';
            submitBtn.disabled = true;
        });
        
        // Auto-resize textarea
        const textarea = form.querySelector('textarea');
        if (textarea) {
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = this.scrollHeight + 'px';
            });
        }
    });
</script>
{% endblock %}
