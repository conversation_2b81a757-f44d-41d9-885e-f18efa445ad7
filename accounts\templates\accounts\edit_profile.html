{% extends 'tasks/base.html' %}

{% block title %}Edit Profile - {{ user.username }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-user-edit me-2"></i>
                        Edit Profile
                    </h4>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                <i class="fas fa-user me-1"></i>{{ form.first_name.label }}
                            </label>
                            {{ form.first_name }}
                            {% if form.first_name.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.first_name.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                <i class="fas fa-user me-1"></i>{{ form.last_name.label }}
                            </label>
                            {{ form.last_name }}
                            {% if form.last_name.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.last_name.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">
                                <i class="fas fa-envelope me-1"></i>{{ form.email.label }}
                            </label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.email.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                This email will be used for task notifications.
                            </small>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Save Changes
                            </button>
                            <a href="{% url 'profile' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Additional Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        Account Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'change_password' %}" class="btn btn-outline-warning">
                            <i class="fas fa-key me-2"></i>
                            Change Password
                        </a>
                        <a href="{% url 'settings' %}" class="btn btn-outline-info">
                            <i class="fas fa-cog me-2"></i>
                            Account Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .form-control {
        background: rgba(113, 128, 150, 0.8);
        border: 1px solid var(--border-color);
        color: var(--text-primary);
    }
    
    .form-control:focus {
        background: rgba(113, 128, 150, 1);
        border-color: var(--accent-blue);
        box-shadow: 0 0 0 0.2rem rgba(66, 153, 225, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
        border: none;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(66, 153, 225, 0.3);
    }
</style>
{% endblock %}
