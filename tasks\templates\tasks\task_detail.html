{% extends 'tasks/base.html' %}

{% block title %}{{ task.title }} - Task Manager{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8 col-md-10">
        <div class="card fade-in-up">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-eye me-2"></i>Task Details
                </h4>
                <div class="btn-group">
                    <a href="{% url 'task_update' task.pk %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-edit me-1"></i>Edit
                    </a>
                    <a href="{% url 'task_delete' task.pk %}" class="btn btn-danger btn-sm">
                        <i class="fas fa-trash me-1"></i>Delete
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12">
                        <h2 class="mb-3">{{ task.title }}</h2>
                        
                        {% if task.category %}
                            <div class="mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-tag me-2 text-muted"></i>
                                    <strong>Category:</strong>
                                    <span class="badge ms-2" style="background-color: {{ task.category.color }};">
                                        {{ task.category.name }}
                                    </span>
                                </div>
                            </div>
                        {% endif %}

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-flag me-2 text-muted"></i>
                                    <strong>Status:</strong>
                                    <span class="badge ms-2" style="background-color: {{ task.status_color }};">
                                        {{ task.get_status_display }}
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-exclamation-circle me-2 text-muted"></i>
                                    <strong>Priority:</strong>
                                    <span class="badge ms-2" style="background-color: {{ task.priority_color }};">
                                        {{ task.get_priority_display }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        {% if task.description %}
                            <div class="mb-4">
                                <h5><i class="fas fa-align-left me-2 text-muted"></i>Description</h5>
                                <div class="p-3 rounded" style="background-color: var(--bg-tertiary); border-left: 4px solid var(--accent-blue);">
                                    {{ task.description|linebreaks }}
                                </div>
                            </div>
                        {% endif %}

                        <div class="row mb-4">
                            {% if task.due_date %}
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-calendar me-2 text-muted"></i>
                                        <strong>Due Date:</strong>
                                        <span class="ms-2">{{ task.due_date|date:"F d, Y \a\t H:i" }}</span>
                                        {% if task.is_overdue %}
                                            <span class="badge bg-danger ms-2">
                                                <i class="fas fa-exclamation-triangle me-1"></i>Overdue
                                            </span>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endif %}
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-clock me-2 text-muted"></i>
                                    <strong>Created:</strong>
                                    <span class="ms-2">{{ task.created_at|date:"F d, Y \a\t H:i" }}</span>
                                </div>
                            </div>
                        </div>

                        {% if task.updated_at != task.created_at %}
                            <div class="mb-4">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-edit me-2 text-muted"></i>
                                    <strong>Last Updated:</strong>
                                    <span class="ms-2">{{ task.updated_at|date:"F d, Y \a\t H:i" }}</span>
                                </div>
                            </div>
                        {% endif %}

                        <!-- Task Steps Section -->
                        {% if task.has_steps %}
                            <div class="mb-4">
                                <h5><i class="fas fa-list-ol me-2 text-muted"></i>Task Steps</h5>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>Progress: {{ task.completed_steps_count }}/{{ task.total_steps_count }} steps completed</span>
                                        <span class="badge" style="background-color: {{ task.progress_color }};">{{ task.progress_percentage }}%</span>
                                    </div>
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar"
                                             style="width: {{ task.progress_percentage }}%; background-color: {{ task.progress_color }};"
                                             id="task-progress-bar">
                                        </div>
                                    </div>
                                </div>

                                <div class="steps-list">
                                    {% for step in task.steps.all %}
                                        <div class="step-item d-flex align-items-start mb-3 p-3 rounded"
                                             style="background-color: var(--bg-tertiary); border-left: 4px solid {% if step.is_completed %}var(--accent-green){% else %}var(--border-color){% endif %};">
                                            <div class="form-check me-3">
                                                <input class="form-check-input step-checkbox"
                                                       type="checkbox"
                                                       {% if step.is_completed %}checked{% endif %}
                                                       data-step-id="{{ step.id }}"
                                                       id="step-{{ step.id }}">
                                            </div>
                                            <div class="flex-grow-1">
                                                <label class="form-check-label {% if step.is_completed %}text-decoration-line-through text-muted{% endif %}"
                                                       for="step-{{ step.id }}">
                                                    <strong>{{ step.title }}</strong>
                                                </label>
                                                {% if step.description %}
                                                    <div class="text-muted mt-1">{{ step.description }}</div>
                                                {% endif %}
                                                {% if step.completed_at %}
                                                    <small class="text-success">
                                                        <i class="fas fa-check me-1"></i>Completed {{ step.completed_at|date:"M d, Y \a\t H:i" }}
                                                    </small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}

                        <div class="d-flex justify-content-between mt-4">
                            <a href="{% url 'task_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Back to Tasks
                            </a>
                            <div>
                                <a href="{% url 'task_update' task.pk %}" class="btn btn-primary me-2">
                                    <i class="fas fa-edit me-1"></i>Edit Task
                                </a>
                                <a href="{% url 'task_delete' task.pk %}" class="btn btn-danger">
                                    <i class="fas fa-trash me-1"></i>Delete Task
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add smooth scroll animation for long descriptions
        const description = document.querySelector('.card-body');
        if (description && description.scrollHeight > description.clientHeight) {
            description.style.maxHeight = '600px';
            description.style.overflowY = 'auto';
        }

        // Add confirmation for delete button
        const deleteBtn = document.querySelector('a[href*="delete"]');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', function(e) {
                if (!confirm('Are you sure you want to delete this task?')) {
                    e.preventDefault();
                }
            });
        }

        // Handle step checkbox changes
        const stepCheckboxes = document.querySelectorAll('.step-checkbox');
        stepCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const stepId = this.dataset.stepId;
                const isChecked = this.checked;

                // Show loading state
                this.disabled = true;

                // Send AJAX request to toggle step
                fetch(`/steps/${stepId}/toggle/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update checkbox state
                        this.checked = data.is_completed;

                        // Update progress bar
                        const progressBar = document.getElementById('task-progress-bar');
                        if (progressBar) {
                            progressBar.style.width = data.progress_percentage + '%';
                            progressBar.style.backgroundColor = data.progress_color;
                        }

                        // Update progress text
                        const progressText = document.querySelector('.steps-list').previousElementSibling.querySelector('span');
                        if (progressText) {
                            progressText.textContent = `Progress: ${data.completed_steps}/${data.total_steps} steps completed`;
                        }

                        // Update progress badge
                        const progressBadge = document.querySelector('.badge');
                        if (progressBadge) {
                            progressBadge.textContent = data.progress_percentage + '%';
                            progressBadge.style.backgroundColor = data.progress_color;
                        }

                        // Update step appearance
                        const stepItem = this.closest('.step-item');
                        const label = stepItem.querySelector('.form-check-label');

                        if (data.is_completed) {
                            label.classList.add('text-decoration-line-through', 'text-muted');
                            stepItem.style.borderLeftColor = 'var(--accent-green)';
                        } else {
                            label.classList.remove('text-decoration-line-through', 'text-muted');
                            stepItem.style.borderLeftColor = 'var(--border-color)';
                        }

                        // Show success animation
                        stepItem.style.transform = 'scale(1.02)';
                        setTimeout(() => {
                            stepItem.style.transform = 'scale(1)';
                        }, 200);

                    } else {
                        // Revert checkbox state on error
                        this.checked = !isChecked;
                        alert('Error updating step: ' + (data.error || 'Unknown error'));
                    }
                })
                .catch(error => {
                    // Revert checkbox state on error
                    this.checked = !isChecked;
                    console.error('Error:', error);
                    alert('Error updating step. Please try again.');
                })
                .finally(() => {
                    // Re-enable checkbox
                    this.disabled = false;
                });
            });
        });
    });
</script>
{% endblock %}
