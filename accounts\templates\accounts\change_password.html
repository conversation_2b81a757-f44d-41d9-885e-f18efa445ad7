{% extends 'tasks/base.html' %}

{% block title %}Change Password - {{ user.username }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-key me-2"></i>
                        Change Password
                    </h4>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Password Requirements:</strong>
                        <ul class="mb-0 mt-2">
                            <li>At least 8 characters long</li>
                            <li>Cannot be too similar to your personal information</li>
                            <li>Cannot be a commonly used password</li>
                            <li>Cannot be entirely numeric</li>
                        </ul>
                    </div>

                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.old_password.id_for_label }}" class="form-label">
                                <i class="fas fa-lock me-1"></i>{{ form.old_password.label }}
                            </label>
                            {{ form.old_password }}
                            {% if form.old_password.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.old_password.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.new_password1.id_for_label }}" class="form-label">
                                <i class="fas fa-key me-1"></i>{{ form.new_password1.label }}
                            </label>
                            {{ form.new_password1 }}
                            {% if form.new_password1.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.new_password1.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.new_password1.help_text %}
                                <small class="form-text text-muted">{{ form.new_password1.help_text }}</small>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.new_password2.id_for_label }}" class="form-label">
                                <i class="fas fa-key me-1"></i>{{ form.new_password2.label }}
                            </label>
                            {{ form.new_password2 }}
                            {% if form.new_password2.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.new_password2.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-key me-2"></i>
                                Change Password
                            </button>
                            <a href="{% url 'profile' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Security Tips -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        Security Tips
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="mb-0">
                        <li>Use a unique password that you don't use elsewhere</li>
                        <li>Consider using a password manager</li>
                        <li>Include a mix of letters, numbers, and symbols</li>
                        <li>Avoid using personal information in your password</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .form-control {
        background: rgba(113, 128, 150, 0.8);
        border: 1px solid var(--border-color);
        color: var(--text-primary);
    }
    
    .form-control:focus {
        background: rgba(113, 128, 150, 1);
        border-color: var(--accent-yellow);
        box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
    }
    
    .btn-warning {
        background: linear-gradient(135deg, var(--accent-yellow), var(--accent-orange));
        border: none;
        color: var(--bg-primary);
        font-weight: 600;
    }
    
    .btn-warning:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
        color: var(--bg-primary);
    }
    
    .alert-info {
        background: rgba(66, 153, 225, 0.1);
        border: 1px solid rgba(66, 153, 225, 0.3);
        color: var(--text-primary);
    }
</style>
{% endblock %}
