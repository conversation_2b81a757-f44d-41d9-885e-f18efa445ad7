from django import forms
from django.contrib.auth.models import User
from django.contrib.auth.forms import UserChangeForm, PasswordChangeForm


class UserProfileForm(forms.ModelForm):
    """Form for editing user profile information"""
    
    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'email']
        widgets = {
            'first_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter your first name'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter your last name'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter your email address'
            }),
        }
        labels = {
            'first_name': 'First Name',
            'last_name': 'Last Name',
            'email': 'Email Address',
        }


class CustomPasswordChangeForm(PasswordChangeForm):
    """Custom password change form with better styling"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add CSS classes to all fields
        for field_name, field in self.fields.items():
            field.widget.attrs.update({
                'class': 'form-control',
                'autocomplete': 'current-password' if field_name == 'old_password' else 'new-password'
            })
        
        # Update field attributes
        self.fields['old_password'].widget.attrs.update({
            'placeholder': 'Enter your current password'
        })
        self.fields['new_password1'].widget.attrs.update({
            'placeholder': 'Enter your new password'
        })
        self.fields['new_password2'].widget.attrs.update({
            'placeholder': 'Confirm your new password'
        })


class UserSettingsForm(forms.Form):
    """Form for user settings and preferences"""
    
    # Email notification preferences
    email_notifications = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        label='Enable email notifications for tasks'
    )
    
    # Task preferences
    default_task_priority = forms.ChoiceField(
        choices=[
            ('low', 'Low'),
            ('medium', 'Medium'),
            ('high', 'High'),
            ('urgent', 'Urgent'),
        ],
        initial='medium',
        widget=forms.Select(attrs={'class': 'form-select'}),
        label='Default task priority'
    )
    
    # Timeline preferences
    default_timeline_view = forms.ChoiceField(
        choices=[
            ('daily', 'Daily View'),
            ('monthly', 'Monthly View'),
        ],
        initial='daily',
        widget=forms.Select(attrs={'class': 'form-select'}),
        label='Default timeline view'
    )
    
    # Theme preferences
    theme_preference = forms.ChoiceField(
        choices=[
            ('dark', 'Dark Theme'),
            ('light', 'Light Theme'),
            ('auto', 'Auto (System)'),
        ],
        initial='dark',
        widget=forms.Select(attrs={'class': 'form-select'}),
        label='Theme preference'
    )
    
    # Task list preferences
    tasks_per_page = forms.ChoiceField(
        choices=[
            ('10', '10 tasks per page'),
            ('25', '25 tasks per page'),
            ('50', '50 tasks per page'),
            ('100', '100 tasks per page'),
        ],
        initial='25',
        widget=forms.Select(attrs={'class': 'form-select'}),
        label='Tasks per page'
    )
