# 📧 Email Notification Setup Guide

## 🎯 Current Status
Your email notifications **ARE WORKING** but are currently set to "console mode" for development. The test showed that:

✅ Status change notifications are triggered correctly
✅ Step completion notifications work perfectly  
✅ Beautiful HTML emails are generated
❌ Emails are only printed to console, not sent to actual email addresses

## 🚀 Quick Setup for Real Email Sending

### Option 1: Gmail (Recommended)

1. **Enable 2-Factor Authentication** on your Google account
2. **Generate App Password**:
   - Go to [Google Account Settings](https://myaccount.google.com/)
   - Security → 2-Step Verification → App passwords
   - Select "Mail" and generate password
   - Copy the 16-character password

3. **Update Settings**:
   - Open `task_manager_project/settings.py`
   - Replace these lines:
   ```python
   EMAIL_HOST_USER = '<EMAIL>'  # Your Gmail address
   EMAIL_HOST_PASSWORD = 'your-app-password'  # The 16-character app password
   ```

4. **Restart Django Server**:
   ```bash
   # Stop current server (Ctrl+C)
   # Then restart:
   python manage.py runserver 0.0.0.0:8000
   ```

### Option 2: Other Email Providers

#### SendGrid
```python
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.sendgrid.net'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = 'apikey'
EMAIL_HOST_PASSWORD = 'your-sendgrid-api-key'
```

#### Outlook/Hotmail
```python
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp-mail.outlook.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-password'
```

## 🧪 Testing After Setup

1. **Test with the script**:
   ```bash
   python check_notifications.py
   ```

2. **Test by changing task status**:
   - Go to any task with status change notifications
   - Change the status and save
   - Check your email inbox (and spam folder)

3. **Test step completion**:
   - Go to a task with step completion notifications
   - Mark a step as completed
   - Check your email

## 🔍 Troubleshooting

### Gmail Issues
- **"Authentication failed"**: Make sure you're using app password, not regular password
- **"Less secure apps"**: Not needed if using app password with 2FA
- **Still not working**: Check spam folder, verify email address is correct

### General Issues
- **Emails not sending**: Check Django server console for error messages
- **Wrong email address**: Verify notification email addresses in task settings
- **Firewall blocking**: Check if port 587 is blocked by firewall

## 📋 Current Notifications in Your System

Based on the test, you have these active notifications:

1. **"Create a business dashboard to Jenna Ltd."**
   - ✅ Status Change → <EMAIL> (Immediate)
   - ✅ Step Completed → <EMAIL> (Immediate)
   - Due Date Reminder → <EMAIL> (1 Day Before)
   - Overdue Alert → <EMAIL> (Daily)

2. **"Test Notification Task"**
   - Status Change → <EMAIL> (Immediate)
   - Due Date Reminder → <EMAIL> (1 Day Before)
   - Overdue Alert → <EMAIL> (Daily)
   - Task Created → <EMAIL> (Immediate)

## 🎉 What Works Right Now

The notification system is **fully functional**:
- ✅ Automatic triggering on status changes
- ✅ Automatic triggering on step completion  
- ✅ Beautiful HTML email templates
- ✅ Management command for scheduled notifications
- ✅ Admin interface for managing notifications

**You just need to configure the email provider to receive actual emails!**

## 🔄 Switch Back to Console Mode

If you want to see emails in console again (for testing):
```python
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
```

## 📞 Need Help?

If you encounter issues:
1. Check the Django server console for error messages
2. Verify your email provider settings
3. Test with a simple email first
4. Check spam/junk folders

The notification system is working perfectly - you just need to connect it to a real email provider! 🚀
