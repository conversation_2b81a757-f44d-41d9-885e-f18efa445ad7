{% extends 'tasks/base.html' %}

{% block title %}Categories - Task Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card fade-in-up">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-tags me-2"></i>Categories
                </h4>
                <a href="{% url 'category_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>New Category
                </a>
            </div>
            <div class="card-body">
                {% if categories %}
                    <div class="row">
                        {% for category in categories %}
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="card h-100 category-card fade-in-up">
                                    <div class="card-body d-flex flex-column">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="category-color-indicator me-2" 
                                                     style="background-color: {{ category.color }}; width: 20px; height: 20px; border-radius: 50%;"></div>
                                                <h5 class="card-title mb-0">{{ category.name }}</h5>
                                            </div>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                        type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-end dropdown-menu-dark">
                                                    <li><a class="dropdown-item" href="{% url 'category_update' category.pk %}">
                                                        <i class="fas fa-edit me-2"></i>Edit
                                                    </a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item text-danger" href="{% url 'category_delete' category.pk %}">
                                                        <i class="fas fa-trash me-2"></i>Delete
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        
                                        {% if category.description %}
                                            <p class="card-text text-muted mb-3">
                                                {{ category.description|truncatewords:15 }}
                                            </p>
                                        {% endif %}
                                        
                                        <div class="mt-auto">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    <i class="fas fa-tasks me-1"></i>
                                                    {{ category.tasks.count }} task{{ category.tasks.count|pluralize }}
                                                </small>
                                                <small class="text-muted">
                                                    <i class="fas fa-clock me-1"></i>
                                                    {{ category.created_at|date:"M d, Y" }}
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No categories found</h4>
                        <p class="text-muted">Create your first category to organize your tasks!</p>
                        <a href="{% url 'category_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Create Category
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Floating Action Button -->
<a href="{% url 'category_create' %}" class="floating-action-btn pulse" title="Create New Category">
    <i class="fas fa-plus"></i>
</a>
{% endblock %}

{% block extra_js %}
<script>
    // Add smooth animations to category cards
    document.addEventListener('DOMContentLoaded', function() {
        const categoryCards = document.querySelectorAll('.category-card');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        categoryCards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.6s ease';
            observer.observe(card);
        });

        // High z-index dropdown fix for categories
        document.addEventListener('shown.bs.dropdown', function (e) {
            const dropdown = e.target.closest('.dropdown');
            const menu = dropdown.querySelector('.dropdown-menu');
            if (menu) {
                menu.style.zIndex = '99999';
            }
        });

        document.addEventListener('hidden.bs.dropdown', function (e) {
            const dropdown = e.target.closest('.dropdown');
            const menu = dropdown.querySelector('.dropdown-menu');
            if (menu) {
                menu.style.zIndex = '9999';
            }
        });
    });
</script>
{% endblock %}
