#!/usr/bin/env python
"""
Test email connection and sending
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'task_manager_project.settings')
django.setup()

from django.core.mail import send_mail
from django.conf import settings
import smtplib
from email.mime.text import MIMEText

def test_django_email():
    print("🧪 Testing Django Email Configuration")
    print("=" * 50)
    
    print(f"📧 Email Backend: {settings.EMAIL_BACKEND}")
    print(f"📧 Email Host: {settings.EMAIL_HOST}")
    print(f"📧 Email Port: {settings.EMAIL_PORT}")
    print(f"📧 Email Use TLS: {settings.EMAIL_USE_TLS}")
    print(f"📧 Email User: {settings.EMAIL_HOST_USER}")
    print(f"📧 Email Password: {'*' * len(settings.EMAIL_HOST_PASSWORD)} (hidden)")
    print(f"📧 Default From Email: {settings.DEFAULT_FROM_EMAIL}")
    print()
    
    try:
        print("📤 Attempting to send test email...")
        
        result = send_mail(
            subject='Test Email from Task Manager',
            message='This is a test email to verify your email configuration is working correctly.',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[settings.EMAIL_HOST_USER],  # Send to yourself
            fail_silently=False,
        )
        
        if result == 1:
            print("✅ SUCCESS! Test email sent successfully!")
            print(f"📬 Check your inbox at {settings.EMAIL_HOST_USER}")
            print("   (Also check spam/junk folder)")
            return True
        else:
            print("❌ FAILED! Email was not sent (no error thrown)")
            return False
            
    except Exception as e:
        print(f"❌ ERROR! Failed to send email: {e}")
        print()
        
        # Provide specific troubleshooting based on error type
        error_str = str(e).lower()
        
        if 'authentication failed' in error_str or 'username and password not accepted' in error_str:
            print("🔧 TROUBLESHOOTING: Authentication Error")
            print("   - Verify your Gmail app password is correct")
            print("   - Make sure 2-factor authentication is enabled")
            print("   - Try generating a new app password")
            print("   - Check if the email address is correct")
            
        elif 'connection refused' in error_str or 'network is unreachable' in error_str:
            print("🔧 TROUBLESHOOTING: Connection Error")
            print("   - Check your internet connection")
            print("   - Verify firewall isn't blocking port 587")
            print("   - Try using port 465 with SSL instead of TLS")
            
        elif 'timeout' in error_str:
            print("🔧 TROUBLESHOOTING: Timeout Error")
            print("   - Check your internet connection")
            print("   - Try again in a few minutes")
            print("   - Consider using a different SMTP server")
            
        else:
            print("🔧 TROUBLESHOOTING: General Error")
            print("   - Double-check all email settings")
            print("   - Try the raw SMTP test below")
            
        return False

def test_raw_smtp():
    print("\n🔧 Testing Raw SMTP Connection")
    print("=" * 50)
    
    try:
        print("📡 Connecting to Gmail SMTP server...")
        
        # Create SMTP connection
        server = smtplib.SMTP('smtp.gmail.com', 587)
        server.starttls()  # Enable TLS
        
        print("✅ Connected successfully!")
        print("🔐 Attempting authentication...")
        
        # Try to login
        server.login(settings.EMAIL_HOST_USER, settings.EMAIL_HOST_PASSWORD)
        print("✅ Authentication successful!")
        
        # Create test message
        msg = MIMEText("Raw SMTP test from Task Manager")
        msg['Subject'] = 'Raw SMTP Test'
        msg['From'] = settings.EMAIL_HOST_USER
        msg['To'] = settings.EMAIL_HOST_USER
        
        print("📤 Sending test message...")
        server.send_message(msg)
        print("✅ Raw SMTP test email sent successfully!")
        
        server.quit()
        return True
        
    except Exception as e:
        print(f"❌ Raw SMTP test failed: {e}")
        return False

def suggest_alternative_settings():
    print("\n🔄 Alternative Gmail Settings to Try")
    print("=" * 50)
    
    print("If the current settings don't work, try these alternatives:")
    print()
    
    print("Option 1 - Gmail with SSL (Port 465):")
    print("EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'")
    print("EMAIL_HOST = 'smtp.gmail.com'")
    print("EMAIL_PORT = 465")
    print("EMAIL_USE_SSL = True")
    print("EMAIL_USE_TLS = False")
    print(f"EMAIL_HOST_USER = '{settings.EMAIL_HOST_USER}'")
    print("EMAIL_HOST_PASSWORD = 'your-app-password'")
    print()
    
    print("Option 2 - Gmail with different TLS settings:")
    print("EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'")
    print("EMAIL_HOST = 'smtp.gmail.com'")
    print("EMAIL_PORT = 587")
    print("EMAIL_USE_TLS = True")
    print("EMAIL_USE_SSL = False")
    print(f"EMAIL_HOST_USER = '{settings.EMAIL_HOST_USER}'")
    print("EMAIL_HOST_PASSWORD = 'your-app-password'")
    print()

if __name__ == "__main__":
    print("📧 Email Configuration Diagnostic Tool")
    print("=" * 60)
    print()
    
    # Test Django email
    django_success = test_django_email()
    
    if not django_success:
        # Test raw SMTP if Django failed
        smtp_success = test_raw_smtp()
        
        if not smtp_success:
            suggest_alternative_settings()
    
    print("\n" + "=" * 60)
    print("🎯 SUMMARY:")
    if django_success:
        print("✅ Your email configuration is working correctly!")
        print("   Notifications should now be sent to your email address.")
    else:
        print("❌ Email configuration needs adjustment.")
        print("   Follow the troubleshooting suggestions above.")
        print("   You can also switch back to console mode for testing:")
        print("   EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'")
    print("=" * 60)
