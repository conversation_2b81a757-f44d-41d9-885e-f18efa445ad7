<div class="task-modal-content">
    <div class="row">
        <div class="col-12">
            <!-- Task Title -->
            <div class="mb-3">
                <label class="form-label">
                    <i class="fas fa-heading me-1"></i>Title
                </label>
                <input type="text" class="form-control" id="modal-task-title" value="{{ task.title }}" readonly>
            </div>

            <!-- Task Description -->
            <div class="mb-3">
                <label class="form-label">
                    <i class="fas fa-align-left me-1"></i>Description
                </label>
                <textarea class="form-control" id="modal-task-description" rows="3" readonly>{{ task.description|default:"" }}</textarea>
            </div>

            <!-- Task Category -->
            <div class="mb-3">
                <label class="form-label">
                    <i class="fas fa-tag me-1"></i>Category
                </label>
                <select class="form-control" id="modal-task-category" disabled>
                    <option value="">No Category</option>
                    {% for category in categories %}
                        <option value="{{ category.id }}" {% if task.category and task.category.id == category.id %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <!-- Task Status -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-flag me-1"></i>Status
                        </label>
                        <select class="form-control" id="modal-task-status" disabled>
                            <option value="pending" {% if task.status == 'pending' %}selected{% endif %}>Pending</option>
                            <option value="in_progress" {% if task.status == 'in_progress' %}selected{% endif %}>In Progress</option>
                            <option value="completed" {% if task.status == 'completed' %}selected{% endif %}>Completed</option>
                            <option value="cancelled" {% if task.status == 'cancelled' %}selected{% endif %}>Cancelled</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <!-- Task Priority -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-exclamation-circle me-1"></i>Priority
                        </label>
                        <select class="form-control" id="modal-task-priority" disabled>
                            <option value="low" {% if task.priority == 'low' %}selected{% endif %}>Low</option>
                            <option value="medium" {% if task.priority == 'medium' %}selected{% endif %}>Medium</option>
                            <option value="high" {% if task.priority == 'high' %}selected{% endif %}>High</option>
                            <option value="urgent" {% if task.priority == 'urgent' %}selected{% endif %}>Urgent</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Due Date -->
            <div class="mb-3">
                <label class="form-label">
                    <i class="fas fa-calendar me-1"></i>Due Date
                </label>
                <input type="datetime-local" class="form-control" id="modal-task-due-date" 
                       value="{% if task.due_date %}{{ task.due_date|date:'Y-m-d\TH:i' }}{% endif %}" readonly>
            </div>

            <!-- Task Progress (if has steps) -->
            {% if task.has_steps %}
                <div class="mb-3">
                    <label class="form-label">
                        <i class="fas fa-list-ol me-1"></i>Progress
                    </label>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>{{ task.completed_steps_count }}/{{ task.total_steps_count }} steps completed</span>
                        <span class="badge" style="background-color: {{ task.progress_color }};">{{ task.progress_percentage }}%</span>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar" 
                             style="width: {{ task.progress_percentage }}%; background-color: {{ task.progress_color }};">
                        </div>
                    </div>
                </div>

                <!-- Task Steps -->
                <div class="mb-3">
                    <h6>Task Steps:</h6>
                    <div class="steps-list" style="max-height: 200px; overflow-y: auto;">
                        {% for step in task.steps.all %}
                            <div class="d-flex align-items-center mb-2 p-2 rounded" 
                                 style="background-color: var(--bg-tertiary);">
                                <div class="form-check me-2">
                                    <input class="form-check-input step-checkbox" 
                                           type="checkbox" 
                                           {% if step.is_completed %}checked{% endif %}
                                           data-step-id="{{ step.id }}"
                                           id="modal-step-{{ step.id }}">
                                </div>
                                <div class="flex-grow-1">
                                    <label class="form-check-label {% if step.is_completed %}text-decoration-line-through text-muted{% endif %}" 
                                           for="modal-step-{{ step.id }}">
                                        {{ step.title }}
                                    </label>
                                    {% if step.description %}
                                        <div class="text-muted small">{{ step.description }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- Task Metadata -->
            <div class="row">
                <div class="col-md-6">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        Created: {{ task.created_at|date:"M d, Y \a\t H:i" }}
                    </small>
                </div>
                <div class="col-md-6">
                    {% if task.updated_at != task.created_at %}
                        <small class="text-muted">
                            <i class="fas fa-edit me-1"></i>
                            Updated: {{ task.updated_at|date:"M d, Y \a\t H:i" }}
                        </small>
                    {% endif %}
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="mt-4 d-flex gap-2">
                <a href="{% url 'task_detail' task.pk %}" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-eye me-1"></i>View Full Details
                </a>
                <a href="{% url 'task_update' task.pk %}" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-external-link-alt me-1"></i>Edit in Form
                </a>
                <a href="{% url 'task_delete' task.pk %}" class="btn btn-outline-danger btn-sm">
                    <i class="fas fa-trash me-1"></i>Delete
                </a>
            </div>
        </div>
    </div>
</div>
