{% extends 'tasks/base.html' %}

{% block title %}Timeline - Task Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card fade-in-up">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>Task Timeline
                </h4>
                <div class="d-flex gap-2">
                    <!-- View Toggle Buttons -->
                    <div class="btn-group" role="group">
                        <a href="?view=daily" class="btn btn-sm {% if view_mode == 'daily' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                            <i class="fas fa-stream me-1"></i>Daily
                        </a>
                        <a href="?view=monthly" class="btn btn-sm {% if view_mode == 'monthly' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                            <i class="fas fa-calendar me-1"></i>Monthly
                        </a>
                    </div>

                    <a href="{% url 'task_create' %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>New Task
                    </a>
                    <a href="{% url 'task_list' %}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-list me-1"></i>List View
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Overdue Tasks Section -->
                {% if overdue_tasks %}
                    <div class="alert alert-danger mb-4">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>Overdue Tasks ({{ overdue_tasks.count }})
                        </h6>
                        <div class="row">
                            {% for task in overdue_tasks %}
                                <div class="col-md-6 mb-2">
                                    <div class="d-flex align-items-center">
                                        {% if task.category %}
                                            <span class="badge me-2" style="background-color: {{ task.category.color }};">
                                                {{ task.category.name }}
                                            </span>
                                        {% endif %}
                                        <a href="{% url 'task_detail' task.pk %}" class="text-decoration-none text-white">
                                            {{ task.title }}
                                        </a>
                                        <small class="text-muted ms-2">
                                            ({{ task.due_date|date:"M d, H:i" }})
                                        </small>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}

                <!-- Timeline Content -->
                {% if view_mode == 'monthly' %}
                    <!-- Monthly View -->
                    <div class="monthly-container">
                        <div class="timeline-header mb-4">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5>
                                    <i class="fas fa-calendar me-2"></i>Monthly View
                                </h5>
                                <div class="timeline-legend">
                                    <span class="legend-item">
                                        <span class="legend-color today"></span>Today
                                    </span>
                                    <span class="legend-item">
                                        <span class="legend-color weekend"></span>Weekend
                                    </span>
                                    <span class="legend-item">
                                        <span class="legend-color past"></span>Past
                                    </span>
                                </div>
                            </div>
                        </div>

                        {% for month in months_data %}
                            <div class="month-calendar mb-5">
                                <div class="month-header mb-3">
                                    <h6 class="mb-0">{{ month.month_name }}</h6>
                                    <small class="text-muted">{{ month.total_tasks }} task{{ month.total_tasks|pluralize }}</small>
                                </div>

                                <div class="calendar-grid">
                                    <!-- Calendar Header -->
                                    <div class="calendar-header">
                                        <div class="calendar-day-header">Sun</div>
                                        <div class="calendar-day-header">Mon</div>
                                        <div class="calendar-day-header">Tue</div>
                                        <div class="calendar-day-header">Wed</div>
                                        <div class="calendar-day-header">Thu</div>
                                        <div class="calendar-day-header">Fri</div>
                                        <div class="calendar-day-header">Sat</div>
                                    </div>

                                    <!-- Calendar Body -->
                                    {% for week in month.calendar_weeks %}
                                        <div class="calendar-week">
                                            {% for day in week %}
                                                {% if day %}
                                                    <div class="calendar-day {% if day.is_today %}today{% endif %} {% if day.is_past %}past{% endif %} {% if day.is_weekend %}weekend{% endif %}">
                                                        <div class="day-number">{{ day.day }}</div>
                                                        {% if day.task_count > 0 %}
                                                            <div class="task-indicator">
                                                                <span class="task-count">{{ day.task_count }}</span>
                                                            </div>
                                                            <div class="day-tasks">
                                                                {% for task in day.tasks|slice:":3" %}
                                                                    <div class="mini-task" title="{{ task.title }}" data-task-id="{{ task.pk }}" onclick="showTaskModal({{ task.pk }})">
                                                                        <span class="task-dot" style="background-color: {% if task.category %}{{ task.category.color }}{% else %}var(--accent-blue){% endif %};"></span>
                                                                        <span class="task-title">{{ task.title|truncatechars:15 }}</span>
                                                                    </div>
                                                                {% endfor %}
                                                                {% if day.task_count > 3 %}
                                                                    <div class="more-tasks">+{{ day.task_count|add:"-3" }} more</div>
                                                                {% endif %}
                                                            </div>
                                                        {% endif %}
                                                    </div>
                                                {% else %}
                                                    <div class="calendar-day empty"></div>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <!-- Daily View (existing) -->
                    <div class="timeline-container">
                        <div class="timeline-header mb-4">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5>
                                    <i class="fas fa-clock me-2"></i>
                                    {{ start_date|date:"M d" }} - {{ end_date|date:"M d, Y" }}
                                </h5>
                                <div class="timeline-legend">
                                    <span class="legend-item">
                                        <span class="legend-color today"></span>Today
                                    </span>
                                    <span class="legend-item">
                                        <span class="legend-color weekend"></span>Weekend
                                    </span>
                                    <span class="legend-item">
                                        <span class="legend-color past"></span>Past
                                    </span>
                                </div>
                            </div>
                        </div>

                    <div class="timeline-scroll">
                        <div class="timeline">
                            {% for day in timeline_data %}
                                <div class="timeline-day {% if day.is_today %}today{% endif %} {% if day.is_past %}past{% endif %} {% if day.is_weekend %}weekend{% endif %}">
                                    <div class="timeline-date">
                                        <div class="date-number">{{ day.date.day }}</div>
                                        <div class="date-month">{{ day.date|date:"M" }}</div>
                                        <div class="date-weekday">{{ day.date|date:"D" }}</div>
                                    </div>
                                    <div class="timeline-content">
                                        {% if day.tasks %}
                                            {% for task in day.tasks %}
                                                <div class="timeline-task" data-task-id="{{ task.pk }}">
                                                    <div class="task-time">
                                                        {{ task.due_date|date:"H:i" }}
                                                    </div>
                                                    <div class="task-info">
                                                        <div class="task-title">
                                                            <a href="{% url 'task_detail' task.pk %}" class="text-decoration-none">
                                                                {{ task.title }}
                                                            </a>
                                                        </div>
                                                        <div class="task-meta">
                                                            {% if task.category %}
                                                                <span class="badge badge-sm me-1" style="background-color: {{ task.category.color }};">
                                                                    {{ task.category.name }}
                                                                </span>
                                                            {% endif %}
                                                            <span class="badge badge-sm" style="background-color: {{ task.status_color }};">
                                                                {{ task.get_status_display }}
                                                            </span>
                                                            <span class="badge badge-sm" style="background-color: {{ task.priority_color }};">
                                                                {{ task.get_priority_display }}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            {% endfor %}
                                        {% else %}
                                            <div class="no-tasks">
                                                <small class="text-muted">No tasks</small>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Tasks without due dates -->
                {% if tasks_no_date %}
                    <div class="mt-5">
                        <h6 class="mb-3">
                            <i class="fas fa-question-circle me-2"></i>
                            Tasks without due dates ({{ tasks_no_date.count }})
                        </h6>
                        <div class="row">
                            {% for task in tasks_no_date %}
                                <div class="col-md-4 mb-3">
                                    <div class="card card-sm">
                                        <div class="card-body p-3">
                                            <h6 class="card-title mb-2">
                                                <a href="{% url 'task_detail' task.pk %}" class="text-decoration-none">
                                                    {{ task.title }}
                                                </a>
                                            </h6>
                                            <div class="d-flex gap-1 flex-wrap">
                                                {% if task.category %}
                                                    <span class="badge badge-sm" style="background-color: {{ task.category.color }};">
                                                        {{ task.category.name }}
                                                    </span>
                                                {% endif %}
                                                <span class="badge badge-sm" style="background-color: {{ task.status_color }};">
                                                    {{ task.get_status_display }}
                                                </span>
                                                <span class="badge badge-sm" style="background-color: {{ task.priority_color }};">
                                                    {{ task.get_priority_display }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Task Detail Modal -->
<div class="modal fade" id="taskModal" tabindex="-1" aria-labelledby="taskModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="background-color: var(--bg-secondary); border: 1px solid var(--border-color);">
            <div class="modal-header" style="border-bottom: 1px solid var(--border-color);">
                <h5 class="modal-title" id="taskModalLabel">
                    <i class="fas fa-tasks me-2"></i>Task Details
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="taskModalBody">
                <!-- Task details will be loaded here -->
                <div class="text-center py-4">
                    <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                    <p class="mt-2 text-muted">Loading task details...</p>
                </div>
            </div>
            <div class="modal-footer" style="border-top: 1px solid var(--border-color);">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Close
                </button>
                <button type="button" class="btn btn-primary" id="editTaskBtn" style="display: none;">
                    <i class="fas fa-edit me-1"></i>Edit Task
                </button>
                <button type="button" class="btn btn-success" id="saveTaskBtn" style="display: none;">
                    <i class="fas fa-save me-1"></i>Save Changes
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-scroll to today's date
        const todayElement = document.querySelector('.timeline-day.today');
        if (todayElement) {
            setTimeout(() => {
                todayElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest',
                    inline: 'center'
                });
            }, 500);
        }

        // Add hover effects to timeline tasks
        const timelineTasks = document.querySelectorAll('.timeline-task');
        timelineTasks.forEach(task => {
            task.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.02)';
            });

            task.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // Add smooth scrolling for timeline
        const timelineScroll = document.querySelector('.timeline-scroll');
        if (timelineScroll) {
            let isScrolling = false;

            timelineScroll.addEventListener('wheel', function(e) {
                if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) return;

                e.preventDefault();
                this.scrollLeft += e.deltaY;
            });
        }

        // Add animation to timeline days and calendar days
        const timelineDays = document.querySelectorAll('.timeline-day, .calendar-day');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        timelineDays.forEach((day, index) => {
            day.style.opacity = '0';
            day.style.transform = 'translateY(20px)';
            day.style.transition = `all 0.6s ease ${index * 0.05}s`;
            observer.observe(day);
        });

        // Add click functionality to mini tasks (removed calendar day click)
        // Individual task clicks are now handled by showTaskModal function

        // Smooth view transitions
        const viewButtons = document.querySelectorAll('.btn-group a');
        viewButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Add loading state
                const icon = this.querySelector('i');
                const originalIcon = icon.className;
                icon.className = 'fas fa-spinner fa-spin me-1';

                // Reset after a short delay (the page will reload anyway)
                setTimeout(() => {
                    icon.className = originalIcon;
                }, 500);
            });
        });
    });

    // Task Modal Functions
    function showTaskModal(taskId) {
        const modal = new bootstrap.Modal(document.getElementById('taskModal'));
        const modalBody = document.getElementById('taskModalBody');
        const modalTitle = document.getElementById('taskModalLabel');
        const editBtn = document.getElementById('editTaskBtn');
        const saveBtn = document.getElementById('saveTaskBtn');

        // Show loading state
        modalBody.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                <p class="mt-2 text-muted">Loading task details...</p>
            </div>
        `;

        // Reset buttons
        editBtn.style.display = 'none';
        saveBtn.style.display = 'none';

        // Show modal
        modal.show();

        // Load task details
        fetch(`/ajax/task/${taskId}/`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    modalBody.innerHTML = data.html;
                    modalTitle.innerHTML = `<i class="fas fa-tasks me-2"></i>${data.title}`;
                    editBtn.style.display = 'inline-block';
                    editBtn.onclick = () => enableTaskEditing(taskId);

                    // Add step toggle functionality
                    const stepCheckboxes = modalBody.querySelectorAll('.step-checkbox');
                    stepCheckboxes.forEach(checkbox => {
                        checkbox.addEventListener('change', function() {
                            toggleStepFromModal(this.dataset.stepId, this.checked);
                        });
                    });
                } else {
                    modalBody.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Error loading task: ${data.error}
                        </div>
                    `;
                }
            })
            .catch(error => {
                modalBody.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Error loading task details. Please try again.
                    </div>
                `;
                console.error('Error:', error);
            });
    }

    function enableTaskEditing(taskId) {
        const modalBody = document.getElementById('taskModalBody');
        const editBtn = document.getElementById('editTaskBtn');
        const saveBtn = document.getElementById('saveTaskBtn');

        // Enable form fields
        const inputs = modalBody.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.removeAttribute('readonly');
            input.removeAttribute('disabled');
        });

        // Update buttons
        editBtn.style.display = 'none';
        saveBtn.style.display = 'inline-block';
        saveBtn.onclick = () => saveTaskChanges(taskId);

        // Add visual indication of edit mode
        modalBody.style.border = '2px solid var(--accent-blue)';
        modalBody.style.borderRadius = '8px';
    }

    function saveTaskChanges(taskId) {
        const modalBody = document.getElementById('taskModalBody');
        const saveBtn = document.getElementById('saveTaskBtn');

        // Show saving state
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';
        saveBtn.disabled = true;

        // Collect form data
        const formData = {
            title: document.getElementById('modal-task-title').value,
            description: document.getElementById('modal-task-description').value,
            category: document.getElementById('modal-task-category').value,
            status: document.getElementById('modal-task-status').value,
            priority: document.getElementById('modal-task-priority').value,
            due_date: document.getElementById('modal-task-due-date').value,
        };

        // Send update request
        fetch(`/ajax/task/${taskId}/update/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                const alert = document.createElement('div');
                alert.className = 'alert alert-success alert-dismissible fade show';
                alert.innerHTML = `
                    <i class="fas fa-check me-2"></i>${data.message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                modalBody.insertBefore(alert, modalBody.firstChild);

                // Update calendar view
                updateCalendarTask(data.task);

                // Reset edit mode
                modalBody.style.border = 'none';
                saveBtn.style.display = 'none';
                document.getElementById('editTaskBtn').style.display = 'inline-block';

                // Disable fields again
                const inputs = modalBody.querySelectorAll('input, textarea, select');
                inputs.forEach(input => {
                    input.setAttribute('readonly', 'readonly');
                    input.setAttribute('disabled', 'disabled');
                });
            } else {
                // Show error message
                const alert = document.createElement('div');
                alert.className = 'alert alert-danger alert-dismissible fade show';
                alert.innerHTML = `
                    <i class="fas fa-exclamation-triangle me-2"></i>Error: ${data.error}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                modalBody.insertBefore(alert, modalBody.firstChild);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            const alert = document.createElement('div');
            alert.className = 'alert alert-danger alert-dismissible fade show';
            alert.innerHTML = `
                <i class="fas fa-exclamation-triangle me-2"></i>Error saving changes. Please try again.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            modalBody.insertBefore(alert, modalBody.firstChild);
        })
        .finally(() => {
            // Reset button state
            saveBtn.innerHTML = '<i class="fas fa-save me-1"></i>Save Changes';
            saveBtn.disabled = false;
        });
    }

    function updateCalendarTask(task) {
        // Update the mini-task display in calendar if visible
        const miniTasks = document.querySelectorAll(`[data-task-id="${task.id}"]`);
        miniTasks.forEach(miniTask => {
            const titleElement = miniTask.querySelector('.task-title');
            if (titleElement) {
                titleElement.textContent = task.title.length > 15 ? task.title.substring(0, 12) + '...' : task.title;
            }

            const dotElement = miniTask.querySelector('.task-dot');
            if (dotElement && task.category_color) {
                dotElement.style.backgroundColor = task.category_color;
            }
        });
    }

    function toggleStepFromModal(stepId, isChecked) {
        // Use existing step toggle functionality
        fetch(`/steps/${stepId}/toggle/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update progress bar in modal
                const progressBar = document.querySelector('#taskModal .progress-bar');
                if (progressBar) {
                    progressBar.style.width = data.progress_percentage + '%';
                    progressBar.style.backgroundColor = data.progress_color;
                }

                // Update progress text
                const progressText = document.querySelector('#taskModal .badge');
                if (progressText) {
                    progressText.textContent = data.progress_percentage + '%';
                    progressText.style.backgroundColor = data.progress_color;
                }
            }
        })
        .catch(error => {
            console.error('Error toggling step:', error);
        });
    }
</script>
{% endblock %}
