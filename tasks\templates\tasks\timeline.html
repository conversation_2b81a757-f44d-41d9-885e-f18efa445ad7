{% extends 'tasks/base.html' %}

{% block title %}Timeline - Task Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card fade-in-up">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>Task Timeline
                </h4>
                <div class="d-flex gap-2">
                    <!-- View Toggle Buttons -->
                    <div class="btn-group" role="group">
                        <a href="?view=daily" class="btn btn-sm {% if view_mode == 'daily' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                            <i class="fas fa-stream me-1"></i>Daily
                        </a>
                        <a href="?view=monthly" class="btn btn-sm {% if view_mode == 'monthly' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                            <i class="fas fa-calendar me-1"></i>Monthly
                        </a>
                    </div>

                    <a href="{% url 'task_create' %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>New Task
                    </a>
                    <a href="{% url 'task_list' %}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-list me-1"></i>List View
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Overdue Tasks Section -->
                {% if overdue_tasks %}
                    <div class="alert alert-danger mb-4">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>Overdue Tasks ({{ overdue_tasks.count }})
                        </h6>
                        <div class="row">
                            {% for task in overdue_tasks %}
                                <div class="col-md-6 mb-2">
                                    <div class="d-flex align-items-center">
                                        {% if task.category %}
                                            <span class="badge me-2" style="background-color: {{ task.category.color }};">
                                                {{ task.category.name }}
                                            </span>
                                        {% endif %}
                                        <a href="{% url 'task_detail' task.pk %}" class="text-decoration-none text-white">
                                            {{ task.title }}
                                        </a>
                                        <small class="text-muted ms-2">
                                            ({{ task.due_date|date:"M d, H:i" }})
                                        </small>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}

                <!-- Timeline Content -->
                {% if view_mode == 'monthly' %}
                    <!-- Monthly View -->
                    <div class="monthly-container">
                        <div class="timeline-header mb-4">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5>
                                    <i class="fas fa-calendar me-2"></i>Monthly View
                                </h5>
                                <div class="timeline-legend">
                                    <span class="legend-item">
                                        <span class="legend-color today"></span>Today
                                    </span>
                                    <span class="legend-item">
                                        <span class="legend-color weekend"></span>Weekend
                                    </span>
                                    <span class="legend-item">
                                        <span class="legend-color past"></span>Past
                                    </span>
                                </div>
                            </div>
                        </div>

                        {% for month in months_data %}
                            <div class="month-calendar mb-5">
                                <div class="month-header mb-3">
                                    <h6 class="mb-0">{{ month.month_name }}</h6>
                                    <small class="text-muted">{{ month.total_tasks }} task{{ month.total_tasks|pluralize }}</small>
                                </div>

                                <div class="calendar-grid">
                                    <!-- Calendar Header -->
                                    <div class="calendar-header">
                                        <div class="calendar-day-header">Sun</div>
                                        <div class="calendar-day-header">Mon</div>
                                        <div class="calendar-day-header">Tue</div>
                                        <div class="calendar-day-header">Wed</div>
                                        <div class="calendar-day-header">Thu</div>
                                        <div class="calendar-day-header">Fri</div>
                                        <div class="calendar-day-header">Sat</div>
                                    </div>

                                    <!-- Calendar Body -->
                                    {% for week in month.calendar_weeks %}
                                        <div class="calendar-week">
                                            {% for day in week %}
                                                {% if day %}
                                                    <div class="calendar-day {% if day.is_today %}today{% endif %} {% if day.is_past %}past{% endif %} {% if day.is_weekend %}weekend{% endif %}">
                                                        <div class="day-number">{{ day.day }}</div>
                                                        {% if day.task_count > 0 %}
                                                            <div class="task-indicator">
                                                                <span class="task-count">{{ day.task_count }}</span>
                                                            </div>
                                                            <div class="day-tasks">
                                                                {% for task in day.tasks|slice:":3" %}
                                                                    <a href="{% url 'task_update' task.pk %}" class="mini-task" title="{{ task.title }}" data-task-id="{{ task.pk }}">
                                                                        <span class="task-dot" style="background-color: {% if task.category %}{{ task.category.color }}{% else %}var(--accent-blue){% endif %};"></span>
                                                                        <span class="task-title">{{ task.title|truncatechars:15 }}</span>
                                                                    </a>
                                                                {% endfor %}
                                                                {% if day.task_count > 3 %}
                                                                    <div class="more-tasks">+{{ day.task_count|add:"-3" }} more</div>
                                                                {% endif %}
                                                            </div>
                                                        {% endif %}
                                                    </div>
                                                {% else %}
                                                    <div class="calendar-day empty"></div>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <!-- Daily View (existing) -->
                    <div class="timeline-container">
                        <div class="timeline-header mb-4">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5>
                                    <i class="fas fa-clock me-2"></i>
                                    {{ start_date|date:"M d" }} - {{ end_date|date:"M d, Y" }}
                                </h5>
                                <div class="timeline-legend">
                                    <span class="legend-item">
                                        <span class="legend-color today"></span>Today
                                    </span>
                                    <span class="legend-item">
                                        <span class="legend-color weekend"></span>Weekend
                                    </span>
                                    <span class="legend-item">
                                        <span class="legend-color past"></span>Past
                                    </span>
                                </div>
                            </div>
                        </div>

                    <div class="timeline-scroll">
                        <div class="timeline">
                            {% for day in timeline_data %}
                                <div class="timeline-day {% if day.is_today %}today{% endif %} {% if day.is_past %}past{% endif %} {% if day.is_weekend %}weekend{% endif %}">
                                    <div class="timeline-date">
                                        <div class="date-number">{{ day.date.day }}</div>
                                        <div class="date-month">{{ day.date|date:"M" }}</div>
                                        <div class="date-weekday">{{ day.date|date:"D" }}</div>
                                    </div>
                                    <div class="timeline-content">
                                        {% if day.tasks %}
                                            {% for task in day.tasks %}
                                                <div class="timeline-task" data-task-id="{{ task.pk }}">
                                                    <div class="task-time">
                                                        {{ task.due_date|date:"H:i" }}
                                                    </div>
                                                    <div class="task-info">
                                                        <div class="task-title">
                                                            <a href="{% url 'task_detail' task.pk %}" class="text-decoration-none">
                                                                {{ task.title }}
                                                            </a>
                                                        </div>
                                                        <div class="task-meta">
                                                            {% if task.category %}
                                                                <span class="badge badge-sm me-1" style="background-color: {{ task.category.color }};">
                                                                    {{ task.category.name }}
                                                                </span>
                                                            {% endif %}
                                                            <span class="badge badge-sm" style="background-color: {{ task.status_color }};">
                                                                {{ task.get_status_display }}
                                                            </span>
                                                            <span class="badge badge-sm" style="background-color: {{ task.priority_color }};">
                                                                {{ task.get_priority_display }}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            {% endfor %}
                                        {% else %}
                                            <div class="no-tasks">
                                                <small class="text-muted">No tasks</small>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Tasks without due dates -->
                {% if tasks_no_date %}
                    <div class="mt-5">
                        <h6 class="mb-3">
                            <i class="fas fa-question-circle me-2"></i>
                            Tasks without due dates ({{ tasks_no_date.count }})
                        </h6>
                        <div class="row">
                            {% for task in tasks_no_date %}
                                <div class="col-md-4 mb-3">
                                    <div class="card card-sm">
                                        <div class="card-body p-3">
                                            <h6 class="card-title mb-2">
                                                <a href="{% url 'task_detail' task.pk %}" class="text-decoration-none">
                                                    {{ task.title }}
                                                </a>
                                            </h6>
                                            <div class="d-flex gap-1 flex-wrap">
                                                {% if task.category %}
                                                    <span class="badge badge-sm" style="background-color: {{ task.category.color }};">
                                                        {{ task.category.name }}
                                                    </span>
                                                {% endif %}
                                                <span class="badge badge-sm" style="background-color: {{ task.status_color }};">
                                                    {{ task.get_status_display }}
                                                </span>
                                                <span class="badge badge-sm" style="background-color: {{ task.priority_color }};">
                                                    {{ task.get_priority_display }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal removed - using direct navigation instead -->
{% endblock %}

<style>
    /* Mini-task link styling for monthly view */
    .mini-task {
        display: flex;
        align-items: center;
        padding: 2px 4px;
        margin: 1px 0;
        border-radius: 3px;
        text-decoration: none !important;
        color: var(--text-primary) !important;
        transition: all 0.2s ease;
        font-size: 11px;
        background: rgba(255, 255, 255, 0.05);
    }

    .mini-task:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateX(2px);
        color: var(--accent-blue) !important;
    }

    .mini-task .task-dot {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        margin-right: 4px;
        flex-shrink: 0;
    }

    .mini-task .task-title {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
    }

    /* Ensure calendar day tasks are properly styled */
    .day-tasks {
        margin-top: 4px;
    }

    .calendar-day {
        position: relative;
        cursor: default; /* Remove pointer cursor since we're not using onclick */
    }

    /* Remove modal-related styles since we're not using modals anymore */
    .calendar-day:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }
</style>

{% block extra_js %}
<script>
    // Global error handler for debugging
    window.addEventListener('error', function(e) {
        console.error('Global error:', e.error, e.filename, e.lineno);
    });

    document.addEventListener('DOMContentLoaded', function() {
        console.log('Timeline page loaded successfully');
        // Auto-scroll to today's date
        const todayElement = document.querySelector('.timeline-day.today');
        if (todayElement) {
            setTimeout(() => {
                todayElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest',
                    inline: 'center'
                });
            }, 500);
        }

        // Add hover effects to timeline tasks
        const timelineTasks = document.querySelectorAll('.timeline-task');
        timelineTasks.forEach(task => {
            task.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.02)';
            });

            task.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // Add smooth scrolling for timeline
        const timelineScroll = document.querySelector('.timeline-scroll');
        if (timelineScroll) {
            let isScrolling = false;

            timelineScroll.addEventListener('wheel', function(e) {
                if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) return;

                e.preventDefault();
                this.scrollLeft += e.deltaY;
            });
        }

        // Add animation to timeline days and calendar days
        const timelineDays = document.querySelectorAll('.timeline-day, .calendar-day');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        timelineDays.forEach((day, index) => {
            day.style.opacity = '0';
            day.style.transform = 'translateY(20px)';
            day.style.transition = `all 0.6s ease ${index * 0.05}s`;
            observer.observe(day);
        });

        // Mini tasks now use direct navigation links - no JavaScript needed

        // Smooth view transitions
        const viewButtons = document.querySelectorAll('.btn-group a');
        viewButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Add loading state
                const icon = this.querySelector('i');
                const originalIcon = icon.className;
                icon.className = 'fas fa-spinner fa-spin me-1';

                // Reset after a short delay (the page will reload anyway)
                setTimeout(() => {
                    icon.className = originalIcon;
                }, 500);
            });
        });
    });

    // Modal functions removed - using direct navigation instead



    // All modal-related functions removed
    // Tasks now use direct navigation to task update pages
</script>
{% endblock %}
