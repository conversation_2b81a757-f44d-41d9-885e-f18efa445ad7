"""
Simple working email backend for Gmail
"""
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from django.core.mail.backends.base import BaseEmailBackend
from django.conf import settings


class SimpleGmailBackend(BaseEmailBackend):
    """
    Simple Gmail backend that works around SSL certificate issues
    """

    def send_messages(self, email_messages):
        """
        Send email messages using raw SMTP (which we know works)
        """
        if not email_messages:
            return 0

        sent_count = 0

        try:
            # Create SMTP connection (same as our working raw test)
            server = smtplib.SMTP('smtp.gmail.com', 587)
            server.starttls()  # Enable TLS
            server.login(settings.EMAIL_HOST_USER, settings.EMAIL_HOST_PASSWORD)

            for message in email_messages:
                try:
                    # Create MIME message
                    msg = MIMEMultipart('alternative')
                    msg['Subject'] = message.subject
                    msg['From'] = message.from_email
                    msg['To'] = ', '.join(message.to)

                    # Add plain text part
                    if hasattr(message, 'body') and message.body:
                        text_part = MIMEText(message.body, 'plain')
                        msg.attach(text_part)

                    # Add HTML part if available
                    for content, mimetype in getattr(message, 'alternatives', []):
                        if mimetype == 'text/html':
                            html_part = MIMEText(content, 'html')
                            msg.attach(html_part)

                    # Send the message
                    server.send_message(msg)
                    sent_count += 1

                except Exception as e:
                    if not self.fail_silently:
                        raise e

            server.quit()

        except Exception as e:
            if not self.fail_silently:
                raise e

        return sent_count
