{% extends 'emails/base_email.html' %}

{% block title %}Due Date Reminder - {{ task.title }}{% endblock %}

{% block header_subtitle %}⏰ Due Date Reminder{% endblock %}

{% block content %}
<h2 style="color: #ffc107; margin-bottom: 20px;">Task Due Soon!</h2>

<p>This is a friendly reminder that the following task is due soon:</p>

<div class="task-info">
    <div class="task-title">{{ task.title }}</div>
    
    {% if task.description %}
    <p style="margin: 10px 0; color: #666;">{{ task.description }}</p>
    {% endif %}
    
    <div class="task-meta">
        <div class="meta-item">
            <span class="meta-label">Status:</span>
            <span class="meta-value status-{{ task.status }}">{{ task.get_status_display }}</span>
        </div>
        <div class="meta-item">
            <span class="meta-label">Priority:</span>
            <span class="meta-value priority-{{ task.priority }}">{{ task.get_priority_display }}</span>
        </div>
        <div class="meta-item">
            <span class="meta-label">Due Date:</span>
            <span class="meta-value" style="font-weight: bold; color: #dc3545;">
                {{ task.due_date|date:"M d, Y \a\t H:i" }}
            </span>
        </div>
        {% if task.category %}
        <div class="meta-item">
            <span class="meta-label">Category:</span>
            <span class="meta-value">{{ task.category.name }}</span>
        </div>
        {% endif %}
    </div>
    
    {% if task.has_steps %}
    <div style="margin-top: 20px;">
        <h4 style="margin-bottom: 10px;">Progress: {{ task.progress_percentage }}%</h4>
        <div class="progress-bar">
            <div class="progress-fill" style="width: {{ task.progress_percentage }}%;"></div>
        </div>
        <p style="font-size: 14px; color: #666;">
            {{ task.completed_steps_count }} of {{ task.total_steps_count }} steps completed
        </p>
        
        {% if task.steps.all %}
        <div class="steps-list">
            <h5 style="margin-bottom: 10px;">Task Steps:</h5>
            {% for step in task.steps.all %}
            <div class="step-item">
                <input type="checkbox" class="step-checkbox" {% if step.is_completed %}checked{% endif %} disabled>
                <span {% if step.is_completed %}class="step-completed"{% endif %}>
                    {{ step.title }}
                </span>
            </div>
            {% endfor %}
        </div>
        {% endif %}
    </div>
    {% endif %}
</div>

<div style="text-align: center; margin: 30px 0;">
    <a href="http://localhost:8000/{{ task.id }}/" class="btn">View Task Details</a>
</div>

<p style="color: #666; font-style: italic;">
    Don't let this task slip by! Take action now to stay on track with your goals.
</p>
{% endblock %}
