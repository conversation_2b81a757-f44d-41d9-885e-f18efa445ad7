from django.contrib import admin
from .models import Task, Category, TaskStep, TaskNotification


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'description', 'color', 'created_at']
    search_fields = ['name', 'description']
    list_editable = ['color']
    ordering = ['name']


class TaskStepInline(admin.TabularInline):
    model = TaskStep
    extra = 1
    fields = ['title', 'description', 'is_completed', 'order']
    ordering = ['order', 'created_at']


class TaskNotificationInline(admin.TabularInline):
    model = TaskNotification
    extra = 1
    fields = ['notification_type', 'recipient_email', 'schedule', 'is_active']
    ordering = ['notification_type']


@admin.register(TaskStep)
class TaskStepAdmin(admin.ModelAdmin):
    list_display = ['title', 'task', 'is_completed', 'order', 'created_at']
    list_filter = ['is_completed', 'created_at']
    search_fields = ['title', 'description', 'task__title']
    list_editable = ['is_completed', 'order']
    ordering = ['task', 'order', 'created_at']


@admin.register(Task)
class TaskAdmin(admin.ModelAdmin):
    list_display = ['title', 'category', 'status',
                    'priority', 'progress_display', 'due_date', 'created_at']
    list_filter = ['status', 'priority', 'category', 'created_at']
    search_fields = ['title', 'description']
    list_editable = ['status', 'priority', 'category']
    date_hierarchy = 'created_at'
    ordering = ['-created_at']
    inlines = [TaskStepInline, TaskNotificationInline]

    def progress_display(self, obj):
        if obj.has_steps:
            return f"{obj.progress_percentage}% ({obj.completed_steps_count}/{obj.total_steps_count})"
        return "No steps"
    progress_display.short_description = "Progress"


@admin.register(TaskNotification)
class TaskNotificationAdmin(admin.ModelAdmin):
    list_display = ['task', 'notification_type', 'recipient_email',
                    'schedule', 'is_active', 'last_sent', 'created_at']
    list_filter = ['notification_type', 'schedule', 'is_active', 'created_at']
    search_fields = ['task__title', 'recipient_email']
    list_editable = ['is_active']
    date_hierarchy = 'created_at'
    ordering = ['-created_at']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('task')
