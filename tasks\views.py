from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.db.models import Q
from .models import Task, Category, TaskStep, TaskNotification
from .forms import TaskForm, CategoryForm, TaskStepFormSet, TaskFormWithNotifications, TaskNotificationFormSet


def task_list(request):
    tasks = Task.objects.all()

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        tasks = tasks.filter(
            Q(title__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # Filter by status
    status_filter = request.GET.get('status')
    if status_filter:
        tasks = tasks.filter(status=status_filter)

    # Filter by priority
    priority_filter = request.GET.get('priority')
    if priority_filter:
        tasks = tasks.filter(priority=priority_filter)

    # Filter by category
    category_filter = request.GET.get('category')
    if category_filter:
        tasks = tasks.filter(category_id=category_filter)

    context = {
        'tasks': tasks,
        'search_query': search_query,
        'status_filter': status_filter,
        'priority_filter': priority_filter,
        'category_filter': category_filter,
        'status_choices': Task.STATUS_CHOICES,
        'priority_choices': Task.PRIORITY_CHOICES,
        'categories': Category.objects.all(),
    }
    return render(request, 'tasks/task_list.html', context)


def task_create(request):
    if request.method == 'POST':
        form = TaskFormWithNotifications(request.POST)
        if form.is_valid():
            task = form.save()
            step_formset = TaskStepFormSet(request.POST, instance=task)
            notification_formset = TaskNotificationFormSet(request.POST, instance=task)

            if step_formset.is_valid():
                step_formset.save()

            if notification_formset.is_valid():
                notification_formset.save()

            messages.success(request, 'Task created successfully!')
            return redirect('task_list')
    else:
        form = TaskFormWithNotifications()
        step_formset = TaskStepFormSet()
        notification_formset = TaskNotificationFormSet()

    return render(request, 'tasks/task_form.html', {
        'form': form,
        'step_formset': step_formset,
        'notification_formset': notification_formset,
        'title': 'Create Task'
    })


def task_update(request, pk):
    task = get_object_or_404(Task, pk=pk)
    if request.method == 'POST':
        form = TaskForm(request.POST, instance=task)
        step_formset = TaskStepFormSet(request.POST, instance=task)
        notification_formset = TaskNotificationFormSet(request.POST, instance=task)

        if form.is_valid() and step_formset.is_valid() and notification_formset.is_valid():
            # Store old status for notification purposes
            old_status = task.status

            form.save()
            step_formset.save()
            notification_formset.save()

            # Send status change notification if status changed
            if old_status != task.status:
                from .notifications import send_status_change_notifications
                send_status_change_notifications(task, old_status)

            messages.success(request, 'Task updated successfully!')
            return redirect('task_list')
    else:
        form = TaskForm(instance=task)
        step_formset = TaskStepFormSet(instance=task)
        notification_formset = TaskNotificationFormSet(instance=task)

    return render(request, 'tasks/task_form.html', {
        'form': form,
        'step_formset': step_formset,
        'notification_formset': notification_formset,
        'title': 'Update Task'
    })


def task_delete(request, pk):
    task = get_object_or_404(Task, pk=pk)
    if request.method == 'POST':
        task.delete()
        messages.success(request, 'Task deleted successfully!')
        return redirect('task_list')

    return render(request, 'tasks/task_confirm_delete.html', {'task': task})


def task_detail(request, pk):
    task = get_object_or_404(Task, pk=pk)
    return render(request, 'tasks/task_detail.html', {'task': task})

# Category Views


def category_list(request):
    categories = Category.objects.all()
    return render(request, 'tasks/category_list.html', {'categories': categories})


def category_create(request):
    if request.method == 'POST':
        form = CategoryForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Category created successfully!')
            return redirect('category_list')
    else:
        form = CategoryForm()

    return render(request, 'tasks/category_form.html', {'form': form, 'title': 'Create Category'})


def category_update(request, pk):
    category = get_object_or_404(Category, pk=pk)
    if request.method == 'POST':
        form = CategoryForm(request.POST, instance=category)
        if form.is_valid():
            form.save()
            messages.success(request, 'Category updated successfully!')
            return redirect('category_list')
    else:
        form = CategoryForm(instance=category)

    return render(request, 'tasks/category_form.html', {'form': form, 'title': 'Update Category'})


def category_delete(request, pk):
    category = get_object_or_404(Category, pk=pk)
    if request.method == 'POST':
        category.delete()
        messages.success(request, 'Category deleted successfully!')
        return redirect('category_list')

    return render(request, 'tasks/category_confirm_delete.html', {'category': category})

# Timeline View


def task_timeline(request):
    from datetime import datetime, timedelta
    from django.utils import timezone
    from collections import defaultdict
    import calendar

    # Get view mode (daily or monthly)
    view_mode = request.GET.get('view', 'daily')

    # Get all tasks with due dates
    tasks_with_dates = Task.objects.filter(
        due_date__isnull=False).order_by('due_date')

    today = timezone.now().date()

    if view_mode == 'monthly':
        # Monthly view logic
        current_month = today.replace(day=1)

        # Get month range (current month + next 2 months)
        months_data = []
        for i in range(3):
            if i == 0:
                month_start = current_month
            else:
                # Calculate next month
                if current_month.month == 12:
                    month_start = current_month.replace(
                        year=current_month.year + 1, month=1)
                else:
                    month_start = current_month.replace(
                        month=current_month.month + 1)
                current_month = month_start

            # Get last day of month
            last_day = calendar.monthrange(
                month_start.year, month_start.month)[1]
            month_end = month_start.replace(day=last_day)

            # Get tasks for this month
            month_tasks = tasks_with_dates.filter(
                due_date__date__gte=month_start,
                due_date__date__lte=month_end
            )

            # Group tasks by date
            tasks_by_date = defaultdict(list)
            for task in month_tasks:
                date_key = task.due_date.date()
                tasks_by_date[date_key].append(task)

            # Create calendar grid
            cal = calendar.monthcalendar(month_start.year, month_start.month)
            calendar_weeks = []

            for week in cal:
                week_days = []
                for day in week:
                    if day == 0:
                        week_days.append(None)
                    else:
                        date_obj = month_start.replace(day=day)
                        week_days.append({
                            'date': date_obj,
                            'day': day,
                            'tasks': tasks_by_date.get(date_obj, []),
                            'is_today': date_obj == today,
                            'is_past': date_obj < today,
                            'is_weekend': date_obj.weekday() >= 5,
                            'task_count': len(tasks_by_date.get(date_obj, [])),
                        })
                calendar_weeks.append(week_days)

            months_data.append({
                'month_start': month_start,
                'month_name': month_start.strftime('%B %Y'),
                'calendar_weeks': calendar_weeks,
                'total_tasks': month_tasks.count(),
            })

        context = {
            'view_mode': 'monthly',
            'months_data': months_data,
            'today': today,
        }

    else:
        # Daily view logic (existing)
        start_date = today - timedelta(days=7)  # Show past 7 days
        end_date = today + timedelta(days=30)   # Show next 30 days

        # Filter tasks within the date range
        tasks_in_range = tasks_with_dates.filter(
            due_date__date__gte=start_date,
            due_date__date__lte=end_date
        )

        # Group tasks by date
        tasks_by_date = defaultdict(list)
        for task in tasks_in_range:
            date_key = task.due_date.date()
            tasks_by_date[date_key].append(task)

        # Create timeline data
        timeline_data = []
        current_date = start_date
        while current_date <= end_date:
            timeline_data.append({
                'date': current_date,
                'tasks': tasks_by_date.get(current_date, []),
                'is_today': current_date == today,
                'is_past': current_date < today,
                'is_weekend': current_date.weekday() >= 5,
            })
            current_date += timedelta(days=1)

        context = {
            'view_mode': 'daily',
            'timeline_data': timeline_data,
            'today': today,
            'start_date': start_date,
            'end_date': end_date,
        }

    # Common data for both views
    # Get overdue tasks
    overdue_tasks = Task.objects.filter(
        due_date__lt=timezone.now(),
        status__in=['pending', 'in_progress']
    ).order_by('due_date')

    # Get tasks without due dates
    tasks_no_date = Task.objects.filter(
        due_date__isnull=True).order_by('-created_at')

    context.update({
        'overdue_tasks': overdue_tasks,
        'tasks_no_date': tasks_no_date,
    })

    return render(request, 'tasks/timeline.html', context)

# Task Step Views


def toggle_step(request, step_id):
    """Toggle the completion status of a task step via AJAX"""
    from django.http import JsonResponse

    if request.method == 'POST':
        try:
            step = get_object_or_404(TaskStep, id=step_id)
            step.is_completed = not step.is_completed
            step.save()

            # Get updated task progress
            task = step.task
            return JsonResponse({
                'success': True,
                'is_completed': step.is_completed,
                'progress_percentage': task.progress_percentage,
                'completed_steps': task.completed_steps_count,
                'total_steps': task.total_steps_count,
                'progress_color': task.progress_color,
            })
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


def get_task_details(request, task_id):
    """Get task details for modal display via AJAX"""
    from django.http import JsonResponse
    from django.template.loader import render_to_string

    try:
        task = get_object_or_404(Task, id=task_id)

        # Render task details HTML
        task_html = render_to_string('tasks/task_modal_content.html', {
            'task': task,
            'categories': Category.objects.all(),
        })

        return JsonResponse({
            'success': True,
            'html': task_html,
            'task_id': task.id,
            'title': task.title,
        })
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


def update_task_ajax(request, task_id):
    """Update task via AJAX"""
    from django.http import JsonResponse
    import json

    if request.method == 'POST':
        try:
            task = get_object_or_404(Task, id=task_id)
            data = json.loads(request.body)

            # Update task fields
            task.title = data.get('title', task.title)
            task.description = data.get('description', task.description)
            task.status = data.get('status', task.status)
            task.priority = data.get('priority', task.priority)

            # Handle category
            category_id = data.get('category')
            if category_id:
                try:
                    task.category = Category.objects.get(id=category_id)
                except Category.DoesNotExist:
                    task.category = None
            else:
                task.category = None

            # Handle due date
            due_date = data.get('due_date')
            if due_date:
                from django.utils.dateparse import parse_datetime
                task.due_date = parse_datetime(due_date)
            else:
                task.due_date = None

            task.save()

            return JsonResponse({
                'success': True,
                'message': 'Task updated successfully!',
                'task': {
                    'id': task.id,
                    'title': task.title,
                    'description': task.description,
                    'status': task.status,
                    'priority': task.priority,
                    'category_name': task.category.name if task.category else None,
                    'category_color': task.category.color if task.category else None,
                    'due_date': task.due_date.isoformat() if task.due_date else None,
                    'progress_percentage': task.progress_percentage,
                }
            })
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})
