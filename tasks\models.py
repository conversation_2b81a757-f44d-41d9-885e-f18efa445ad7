from django.db import models
from django.utils import timezone


class Category(models.Model):
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    color = models.CharField(
        max_length=7, default='#007bff', help_text='Hex color code (e.g., #007bff)')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['name']
        verbose_name_plural = 'Categories'

    def __str__(self):
        return self.name


class Task(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    title = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    category = models.ForeignKey(
        Category, on_delete=models.SET_NULL, null=True, blank=True, related_name='tasks')
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='pending')
    priority = models.CharField(
        max_length=20, choices=PRIORITY_CHOICES, default='medium')
    due_date = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    @property
    def is_overdue(self):
        if self.due_date and self.status != 'completed':
            return timezone.now() > self.due_date
        return False

    @property
    def status_color(self):
        colors = {
            'pending': '#6c757d',
            'in_progress': '#007bff',
            'completed': '#28a745',
            'cancelled': '#dc3545',
        }
        return colors.get(self.status, '#6c757d')

    @property
    def priority_color(self):
        colors = {
            'low': '#28a745',
            'medium': '#ffc107',
            'high': '#fd7e14',
            'urgent': '#dc3545',
        }
        return colors.get(self.priority, '#6c757d')

    @property
    def progress_percentage(self):
        """Calculate the percentage of completed steps"""
        total_steps = self.steps.count()
        if total_steps == 0:
            return 0
        completed_steps = self.steps.filter(is_completed=True).count()
        return round((completed_steps / total_steps) * 100)

    @property
    def completed_steps_count(self):
        """Get the number of completed steps"""
        return self.steps.filter(is_completed=True).count()

    @property
    def total_steps_count(self):
        """Get the total number of steps"""
        return self.steps.count()

    @property
    def has_steps(self):
        """Check if task has any steps"""
        return self.steps.exists()

    @property
    def progress_color(self):
        """Get color based on progress percentage"""
        progress = self.progress_percentage
        if progress == 0:
            return '#6c757d'  # Gray
        elif progress < 25:
            return '#dc3545'  # Red
        elif progress < 50:
            return '#fd7e14'  # Orange
        elif progress < 75:
            return '#ffc107'  # Yellow
        elif progress < 100:
            return '#007bff'  # Blue
        else:
            return '#28a745'  # Green


class TaskStep(models.Model):
    task = models.ForeignKey(
        Task, on_delete=models.CASCADE, related_name='steps')
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    is_completed = models.BooleanField(default=False)
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        ordering = ['order', 'created_at']

    def __str__(self):
        return f"{self.task.title} - {self.title}"

    def save(self, *args, **kwargs):
        if self.is_completed and not self.completed_at:
            self.completed_at = timezone.now()
        elif not self.is_completed:
            self.completed_at = None
        super().save(*args, **kwargs)
