from django.db import models
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from django.contrib.auth.models import User


class Category(models.Model):
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    color = models.CharField(
        max_length=7, default='#007bff', help_text='Hex color code (e.g., #007bff)')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['name']
        verbose_name_plural = 'Categories'

    def __str__(self):
        return self.name


class Task(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='tasks')
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    category = models.ForeignKey(
        Category, on_delete=models.SET_NULL, null=True, blank=True, related_name='tasks')
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='pending')
    priority = models.CharField(
        max_length=20, choices=PRIORITY_CHOICES, default='medium')
    due_date = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    completed_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        if self.status == 'completed' and not self.completed_at:
            self.completed_at = timezone.now()
        elif self.status != 'completed':
            self.completed_at = None
        super().save(*args, **kwargs)

    @property
    def is_overdue(self):
        if self.due_date and self.status != 'completed':
            return timezone.now() > self.due_date
        return False

    @property
    def status_color(self):
        colors = {
            'pending': '#6c757d',
            'in_progress': '#007bff',
            'completed': '#28a745',
            'cancelled': '#dc3545',
        }
        return colors.get(self.status, '#6c757d')

    @property
    def priority_color(self):
        colors = {
            'low': '#28a745',
            'medium': '#ffc107',
            'high': '#fd7e14',
            'urgent': '#dc3545',
        }
        return colors.get(self.priority, '#6c757d')

    @property
    def progress_percentage(self):
        """Calculate the percentage of completed steps"""
        total_steps = self.steps.count()
        if total_steps == 0:
            return 0
        completed_steps = self.steps.filter(is_completed=True).count()
        return round((completed_steps / total_steps) * 100)

    @property
    def completed_steps_count(self):
        """Get the number of completed steps"""
        return self.steps.filter(is_completed=True).count()

    @property
    def total_steps_count(self):
        """Get the total number of steps"""
        return self.steps.count()

    @property
    def has_steps(self):
        """Check if task has any steps"""
        return self.steps.exists()

    @property
    def progress_color(self):
        """Get color based on progress percentage"""
        progress = self.progress_percentage
        if progress == 0:
            return '#6c757d'  # Gray
        elif progress < 25:
            return '#dc3545'  # Red
        elif progress < 50:
            return '#fd7e14'  # Orange
        elif progress < 75:
            return '#ffc107'  # Yellow
        elif progress < 100:
            return '#007bff'  # Blue
        else:
            return '#28a745'  # Green


class TaskStep(models.Model):
    task = models.ForeignKey(
        Task, on_delete=models.CASCADE, related_name='steps')
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    is_completed = models.BooleanField(default=False)
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        ordering = ['order', 'created_at']

    def __str__(self):
        return f"{self.task.title} - {self.title}"

    def save(self, *args, **kwargs):
        if self.is_completed and not self.completed_at:
            self.completed_at = timezone.now()
        elif not self.is_completed:
            self.completed_at = None
        super().save(*args, **kwargs)


class TaskNotification(models.Model):
    """Model to store email notification settings for tasks"""

    NOTIFICATION_TYPE_CHOICES = [
        ('due_date', 'Due Date Reminder'),
        ('overdue', 'Overdue Alert'),
        ('status_change', 'Status Change'),
        ('step_completed', 'Step Completed'),
        ('task_created', 'Task Created'),
    ]

    SCHEDULE_CHOICES = [
        ('immediate', 'Immediate'),
        ('1_hour', '1 Hour Before'),
        ('2_hours', '2 Hours Before'),
        ('6_hours', '6 Hours Before'),
        ('12_hours', '12 Hours Before'),
        ('1_day', '1 Day Before'),
        ('2_days', '2 Days Before'),
        ('1_week', '1 Week Before'),
        ('daily', 'Daily Digest'),
        ('weekly', 'Weekly Digest'),
    ]

    task = models.ForeignKey(
        Task, on_delete=models.CASCADE, related_name='notifications')
    notification_type = models.CharField(
        max_length=20, choices=NOTIFICATION_TYPE_CHOICES)
    recipient_email = models.EmailField()
    schedule = models.CharField(
        max_length=20, choices=SCHEDULE_CHOICES, default='immediate')
    is_active = models.BooleanField(default=True)
    last_sent = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['task', 'notification_type']
        unique_together = ['task', 'notification_type', 'recipient_email']

    def __str__(self):
        return f"{self.task.title} - {self.get_notification_type_display()} to {self.recipient_email}"

    def should_send_notification(self):
        """Check if notification should be sent based on schedule and task status"""
        if not self.is_active:
            return False

        now = timezone.now()

        # For immediate notifications
        if self.schedule == 'immediate':
            return True

        # For due date reminders
        if self.notification_type == 'due_date' and self.task.due_date:
            time_until_due = self.task.due_date - now

            if self.schedule == '1_hour' and time_until_due.total_seconds() <= 3600:
                return True
            elif self.schedule == '2_hours' and time_until_due.total_seconds() <= 7200:
                return True
            elif self.schedule == '6_hours' and time_until_due.total_seconds() <= 21600:
                return True
            elif self.schedule == '12_hours' and time_until_due.total_seconds() <= 43200:
                return True
            elif self.schedule == '1_day' and time_until_due.days <= 1:
                return True
            elif self.schedule == '2_days' and time_until_due.days <= 2:
                return True
            elif self.schedule == '1_week' and time_until_due.days <= 7:
                return True

        # For overdue notifications
        if self.notification_type == 'overdue' and self.task.is_overdue:
            # Send daily for overdue tasks
            if not self.last_sent or (now - self.last_sent).days >= 1:
                return True

        return False

    def send_notification(self):
        """Send the notification email"""
        if not self.should_send_notification():
            return False

        try:
            subject = self.get_email_subject()
            message = self.get_email_message()
            html_message = self.get_html_email_message()

            send_mail(
                subject=subject,
                message=message,
                from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
                recipient_list=[self.recipient_email],
                html_message=html_message,
                fail_silently=False,
            )

            self.last_sent = timezone.now()
            self.save(update_fields=['last_sent'])
            return True

        except Exception as e:
            # Log the error (you might want to use proper logging here)
            print(f"Failed to send notification: {e}")
            return False

    def get_email_subject(self):
        """Generate email subject based on notification type"""
        task_title = self.task.title

        subjects = {
            'due_date': f"Reminder: '{task_title}' is due soon",
            'overdue': f"Overdue: '{task_title}' needs attention",
            'status_change': f"Status Update: '{task_title}'",
            'step_completed': f"Progress Update: '{task_title}'",
            'task_created': f"New Task: '{task_title}'",
        }

        return subjects.get(self.notification_type, f"Task Notification: '{task_title}'")

    def get_email_message(self):
        """Generate plain text email message"""
        task = self.task

        message = f"""
Task Notification

Task: {task.title}
Status: {task.get_status_display()}
Priority: {task.get_priority_display()}
"""

        if task.due_date:
            message += f"Due Date: {task.due_date.strftime('%Y-%m-%d %H:%M')}\n"

        if task.description:
            message += f"Description: {task.description}\n"

        if task.category:
            message += f"Category: {task.category.name}\n"

        # Add progress information if task has steps
        if task.has_steps:
            message += f"Progress: {task.progress_percentage}% ({task.completed_steps_count}/{task.total_steps_count} steps completed)\n"

        message += f"\nView task details: http://localhost:8000/{task.id}/\n"

        return message

    def get_html_email_message(self):
        """Generate HTML email message using templates"""
        from django.template.loader import render_to_string

        template_map = {
            'due_date': 'emails/due_date_reminder.html',
            'overdue': 'emails/overdue_alert.html',
            'status_change': 'emails/status_change.html',
            'step_completed': 'emails/status_change.html',  # Reuse status change template
            'task_created': 'emails/status_change.html',    # Reuse status change template
        }

        template_name = template_map.get(self.notification_type, 'emails/base_email.html')

        try:
            return render_to_string(template_name, {
                'task': self.task,
                'notification': self,
            })
        except Exception as e:
            print(f"Failed to render email template: {e}")
            return None
