{% extends 'emails/base_email.html' %}

{% block title %}Overdue Alert - {{ task.title }}{% endblock %}

{% block header_subtitle %}🚨 Overdue Alert{% endblock %}

{% block content %}
<h2 style="color: #dc3545; margin-bottom: 20px;">Task Overdue!</h2>

<p style="color: #dc3545; font-weight: bold;">
    The following task is now overdue and requires immediate attention:
</p>

<div class="task-info" style="border-left-color: #dc3545;">
    <div class="task-title" style="color: #dc3545;">{{ task.title }}</div>
    
    {% if task.description %}
    <p style="margin: 10px 0; color: #666;">{{ task.description }}</p>
    {% endif %}
    
    <div class="task-meta">
        <div class="meta-item">
            <span class="meta-label">Status:</span>
            <span class="meta-value status-{{ task.status }}">{{ task.get_status_display }}</span>
        </div>
        <div class="meta-item">
            <span class="meta-label">Priority:</span>
            <span class="meta-value priority-{{ task.priority }}">{{ task.get_priority_display }}</span>
        </div>
        <div class="meta-item">
            <span class="meta-label">Was Due:</span>
            <span class="meta-value" style="font-weight: bold; color: #dc3545;">
                {{ task.due_date|date:"M d, Y \a\t H:i" }}
            </span>
        </div>
        {% if task.category %}
        <div class="meta-item">
            <span class="meta-label">Category:</span>
            <span class="meta-value">{{ task.category.name }}</span>
        </div>
        {% endif %}
    </div>
    
    {% if task.has_steps %}
    <div style="margin-top: 20px;">
        <h4 style="margin-bottom: 10px;">Current Progress: {{ task.progress_percentage }}%</h4>
        <div class="progress-bar">
            <div class="progress-fill" style="width: {{ task.progress_percentage }}%; background: linear-gradient(90deg, #dc3545, #c82333);"></div>
        </div>
        <p style="font-size: 14px; color: #666;">
            {{ task.completed_steps_count }} of {{ task.total_steps_count }} steps completed
        </p>
    </div>
    {% endif %}
</div>

<div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 6px; padding: 15px; margin: 20px 0; color: #721c24;">
    <h4 style="margin-top: 0;">⚠️ Action Required</h4>
    <p style="margin-bottom: 0;">
        This task is overdue. Please review and update the task status or extend the due date if needed.
    </p>
</div>

<div style="text-align: center; margin: 30px 0;">
    <a href="http://localhost:8000/{{ task.id }}/" class="btn" style="background: #dc3545;">
        Update Task Now
    </a>
</div>

<p style="color: #666; font-style: italic;">
    Overdue tasks can impact your productivity and goals. Take action today!
</p>
{% endblock %}
