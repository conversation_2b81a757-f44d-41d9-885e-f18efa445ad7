{% extends 'tasks/base.html' %}

{% block title %}{{ title }} - Task Manager{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8 col-md-10">
        <div class="card fade-in-up">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-{% if 'Create' in title %}plus{% else %}edit{% endif %} me-2"></i>
                    {{ title }}
                </h4>
            </div>
            <div class="card-body">
                <form method="POST" id="taskForm">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.title.id_for_label }}" class="form-label">
                            <i class="fas fa-heading me-1"></i>Title *
                        </label>
                        {{ form.title }}
                        {% if form.title.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.title.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">
                            <i class="fas fa-align-left me-1"></i>Description
                        </label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.description.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.category.id_for_label }}" class="form-label">
                            <i class="fas fa-tag me-1"></i>Category
                        </label>
                        {{ form.category }}
                        {% if form.category.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.category.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">
                            <a href="{% url 'category_create' %}" class="text-decoration-none">Create new category</a>
                        </small>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.status.id_for_label }}" class="form-label">
                                <i class="fas fa-flag me-1"></i>Status
                            </label>
                            {{ form.status }}
                            {% if form.status.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.status.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="{{ form.priority.id_for_label }}" class="form-label">
                                <i class="fas fa-exclamation-circle me-1"></i>Priority
                            </label>
                            {{ form.priority }}
                            {% if form.priority.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.priority.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="{{ form.due_date.id_for_label }}" class="form-label">
                            <i class="fas fa-calendar me-1"></i>Due Date
                        </label>
                        {{ form.due_date }}
                        {% if form.due_date.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.due_date.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">
                            Leave empty if no due date is required.
                        </small>
                    </div>

                    <!-- Task Steps Section -->
                    <div class="mb-4">
                        <h6 class="mb-3">
                            <i class="fas fa-list-ol me-2"></i>Task Steps
                            <small class="text-muted">(Optional)</small>
                        </h6>

                        <div id="step-formset">
                            {{ step_formset.management_form }}
                            {% for form in step_formset %}
                                <div class="step-form mb-3 p-3 rounded" style="background-color: var(--bg-tertiary); border: 1px solid var(--border-color);">
                                    {% if form.non_field_errors %}
                                        <div class="alert alert-danger">
                                            {{ form.non_field_errors }}
                                        </div>
                                    {% endif %}

                                    <div class="row">
                                        <div class="col-md-6 mb-2">
                                            <label class="form-label">Step Title</label>
                                            {{ form.title }}
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <label class="form-label">Order</label>
                                            {{ form.order }}
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <div class="form-check mt-4">
                                                {{ form.is_completed }}
                                                <label class="form-check-label" for="{{ form.is_completed.id_for_label }}">
                                                    Completed
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-2">
                                        <label class="form-label">Description</label>
                                        {{ form.description }}
                                    </div>

                                    {% if form.DELETE %}
                                        <div class="form-check">
                                            {{ form.DELETE }}
                                            <label class="form-check-label text-danger" for="{{ form.DELETE.id_for_label }}">
                                                <i class="fas fa-trash me-1"></i>Delete this step
                                            </label>
                                        </div>
                                    {% endif %}

                                    {% for hidden in form.hidden_fields %}
                                        {{ hidden }}
                                    {% endfor %}
                                </div>
                            {% endfor %}
                        </div>

                        <button type="button" class="btn btn-outline-primary btn-sm" id="add-step">
                            <i class="fas fa-plus me-1"></i>Add Step
                        </button>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'task_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            {% if 'Create' in title %}Create Task{% else %}Update Task{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add form validation feedback
        const form = document.getElementById('taskForm');
        const inputs = form.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.value.trim() !== '') {
                    this.classList.add('is-valid');
                    this.classList.remove('is-invalid');
                } else if (this.hasAttribute('required')) {
                    this.classList.add('is-invalid');
                    this.classList.remove('is-valid');
                }
            });
            
            input.addEventListener('input', function() {
                if (this.classList.contains('is-invalid') && this.value.trim() !== '') {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                }
            });
        });
        
        // Form submission animation
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';
            submitBtn.disabled = true;
        });
        
        // Auto-resize textarea
        const textarea = form.querySelector('textarea');
        if (textarea) {
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = this.scrollHeight + 'px';
            });
        }

        // Dynamic step management
        const addStepBtn = document.getElementById('add-step');
        const stepFormset = document.getElementById('step-formset');

        if (addStepBtn && stepFormset) {
            addStepBtn.addEventListener('click', function() {
                const totalForms = document.querySelector('#id_steps-TOTAL_FORMS');
                const formNum = parseInt(totalForms.value);

                // Clone the last form
                const lastForm = stepFormset.querySelector('.step-form:last-of-type');
                if (lastForm) {
                    const newForm = lastForm.cloneNode(true);

                    // Update form indices
                    const formRegex = new RegExp(`steps-(\\d+)-`, 'g');
                    newForm.innerHTML = newForm.innerHTML.replace(formRegex, `steps-${formNum}-`);

                    // Clear form values
                    const inputs = newForm.querySelectorAll('input, textarea');
                    inputs.forEach(input => {
                        if (input.type === 'checkbox') {
                            input.checked = false;
                        } else if (input.type !== 'hidden') {
                            input.value = '';
                        }
                    });

                    // Set default order
                    const orderInput = newForm.querySelector('input[name$="-order"]');
                    if (orderInput) {
                        orderInput.value = formNum;
                    }

                    // Insert new form
                    stepFormset.insertBefore(newForm, addStepBtn);

                    // Update total forms count
                    totalForms.value = formNum + 1;

                    // Add animation
                    newForm.style.opacity = '0';
                    newForm.style.transform = 'translateY(-20px)';
                    setTimeout(() => {
                        newForm.style.transition = 'all 0.3s ease';
                        newForm.style.opacity = '1';
                        newForm.style.transform = 'translateY(0)';
                    }, 10);
                }
            });
        }

        // Auto-resize step description textareas
        const stepTextareas = document.querySelectorAll('#step-formset textarea');
        stepTextareas.forEach(textarea => {
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = this.scrollHeight + 'px';
            });
        });
    });
</script>
{% endblock %}
