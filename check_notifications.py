#!/usr/bin/env python
"""
Check existing notifications and test email sending
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'task_manager_project.settings')
django.setup()

from tasks.models import TaskNotification, Task, TaskStep
from django.conf import settings

def check_notifications():
    print("🔍 Checking Task Notifications")
    print("=" * 50)
    
    # Check email backend
    print(f"📧 Email Backend: {settings.EMAIL_BACKEND}")
    if 'console' in settings.EMAIL_BACKEND.lower():
        print("⚠️  WARNING: Using console backend - emails will only appear in server console!")
        print("   To receive actual emails, configure SMTP settings in settings.py")
    print()
    
    # Check all notifications
    notifications = TaskNotification.objects.all().order_by('task__id', 'notification_type')
    print(f"📋 Total Notifications: {notifications.count()}")
    print()
    
    if notifications.exists():
        for notif in notifications:
            status = "✅ Active" if notif.is_active else "❌ Inactive"
            last_sent = notif.last_sent.strftime('%Y-%m-%d %H:%M') if notif.last_sent else "Never"
            print(f"{status} | Task: {notif.task.title}")
            print(f"     Type: {notif.get_notification_type_display()}")
            print(f"     Email: {notif.recipient_email}")
            print(f"     Schedule: {notif.get_schedule_display()}")
            print(f"     Last Sent: {last_sent}")
            print()
    else:
        print("❌ No notifications found!")
        print("   Create notifications by:")
        print("   1. Adding email when creating a task")
        print("   2. Editing a task and adding notifications")
        print()
    
    # Check recent tasks
    recent_tasks = Task.objects.all().order_by('-id')[:5]
    print(f"📝 Recent Tasks:")
    for task in recent_tasks:
        notif_count = task.notifications.count()
        print(f"   {task.id}. {task.title} - {notif_count} notifications")
    print()
    
    return notifications

def test_status_change_notification():
    print("🧪 Testing Status Change Notification")
    print("=" * 50)
    
    # Find a task with status change notifications
    status_notifications = TaskNotification.objects.filter(
        notification_type='status_change',
        is_active=True
    )
    
    if not status_notifications.exists():
        print("❌ No active status change notifications found!")
        return False
    
    # Get the first task with status change notification
    notification = status_notifications.first()
    task = notification.task
    
    print(f"📋 Testing with task: {task.title}")
    print(f"📧 Email: {notification.recipient_email}")
    print(f"🔄 Current status: {task.get_status_display()}")
    
    # Store original status
    original_status = task.status
    
    # Change status to trigger notification
    new_status = 'in_progress' if original_status != 'in_progress' else 'completed'
    task.status = new_status
    task.save()
    
    print(f"✅ Changed status to: {task.get_status_display()}")
    print("📧 Check your email or server console for notification!")
    
    # Change back to original status
    task.status = original_status
    task.save()
    print(f"🔄 Restored original status: {task.get_status_display()}")
    
    return True

def test_step_completion_notification():
    print("🧪 Testing Step Completion Notification")
    print("=" * 50)
    
    # Find a task with step completion notifications and steps
    step_notifications = TaskNotification.objects.filter(
        notification_type='step_completed',
        is_active=True,
        task__steps__isnull=False
    ).distinct()
    
    if not step_notifications.exists():
        print("❌ No active step completion notifications found!")
        return False
    
    # Get the first task with step completion notification
    notification = step_notifications.first()
    task = notification.task
    
    # Find an incomplete step
    incomplete_step = task.steps.filter(is_completed=False).first()
    
    if not incomplete_step:
        print("❌ No incomplete steps found!")
        return False
    
    print(f"📋 Testing with task: {task.title}")
    print(f"📧 Email: {notification.recipient_email}")
    print(f"📝 Step: {incomplete_step.title}")
    
    # Complete the step
    incomplete_step.is_completed = True
    incomplete_step.save()
    
    print(f"✅ Completed step: {incomplete_step.title}")
    print("📧 Check your email or server console for notification!")
    
    # Uncomplete the step
    incomplete_step.is_completed = False
    incomplete_step.save()
    print(f"🔄 Restored step to incomplete")
    
    return True

if __name__ == "__main__":
    notifications = check_notifications()
    
    if notifications.exists():
        print("🧪 Running Tests...")
        print()
        
        # Test status change
        if test_status_change_notification():
            print()
        
        # Test step completion
        if test_step_completion_notification():
            print()
        
        print("✅ Tests completed!")
        print()
        print("💡 Tips:")
        print("   - If using console backend, check the Django server terminal for emails")
        print("   - If using SMTP, check your email inbox (and spam folder)")
        print("   - Verify notification email addresses are correct")
    else:
        print("❌ No notifications to test!")
        print("   Please create some notifications first.")
