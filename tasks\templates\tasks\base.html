<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Task Manager{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --bg-primary: #1a202c;
            --bg-secondary: #2d3748;
            --bg-tertiary: #4a5568;
            --text-primary: #f7fafc;
            --text-secondary: #a0aec0;
            --accent-blue: #4299e1;
            --accent-green: #48bb78;
            --accent-red: #f56565;
            --accent-yellow: #ed8936;
            --accent-purple: #9f7aea;
            --border-color: #4a5568;
            --hover-bg: #2d3748;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(88, 166, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(63, 185, 80, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(165, 165, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .navbar {
            background: rgba(45, 55, 72, 0.95) !important;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .navbar-brand {
            color: var(--accent-blue) !important;
            font-weight: bold;
            font-size: 1.5rem;
        }

        .nav-link {
            color: var(--text-secondary) !important;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: var(--accent-blue) !important;
            text-shadow: 0 0 8px rgba(88, 166, 255, 0.5);
        }

        .dropdown-menu {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            z-index: 9999 !important;
            position: absolute !important;
        }

        /* Move navbar dropdown further right */
        .navbar .dropdown-menu {
            right: 0 !important;
            left: auto !important;
            transform: translateX(0) !important;
            margin-right: 0 !important;
            min-width: 200px;
        }

        .navbar .dropdown {
            position: relative;
            z-index: 9999;
        }

        .navbar .dropdown-toggle {
            z-index: 9999;
        }

        .dropdown-item {
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .dropdown-item:hover {
            background: var(--hover-bg);
            color: var(--accent-blue);
        }

        .dropdown-divider {
            border-color: var(--border-color);
        }

        /* Global dropdown z-index fix */
        .dropdown-menu.show {
            z-index: 99999 !important;
            position: absolute !important;
        }

        /* Ensure navbar dropdowns are always on top */
        .navbar .dropdown-menu {
            z-index: 99999 !important;
        }

        /* Fix for any potential conflicts with cards or other elements */
        .card {
            z-index: 1;
        }

        .task-card {
            z-index: 1;
        }

        .container {
            max-width: 1200px;
        }

        .card {
            background: rgba(74, 85, 104, 0.9);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(66, 153, 225, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            border-color: rgba(66, 153, 225, 0.6);
            background: rgba(74, 85, 104, 1);
        }

        .card-header {
            background: rgba(45, 55, 72, 0.9);
            border-bottom: 1px solid var(--border-color);
            color: var(--text-primary);
            font-weight: 600;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--accent-blue), #4493f8);
            border: none;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #4493f8, var(--accent-blue));
            transform: scale(1.02);
            box-shadow: 0 6px 20px rgba(88, 166, 255, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--accent-green), #2ea043);
            border: none;
            border-radius: 8px;
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--accent-red), #da3633);
            border: none;
            border-radius: 8px;
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--accent-yellow), #bb8009);
            border: none;
            border-radius: 8px;
        }

        .form-control, .form-select {
            background: rgba(113, 128, 150, 0.8);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            background: rgba(113, 128, 150, 1);
            border-color: var(--accent-blue);
            color: var(--text-primary);
            box-shadow: 0 0 0 0.2rem rgba(66, 153, 225, 0.25);
        }

        .form-control::placeholder {
            color: var(--text-secondary);
        }

        .alert {
            border-radius: 8px;
            border: none;
        }

        .alert-success {
            background: rgba(63, 185, 80, 0.2);
            color: var(--accent-green);
            border-left: 4px solid var(--accent-green);
        }

        .alert-danger {
            background: rgba(248, 81, 73, 0.2);
            color: var(--accent-red);
            border-left: 4px solid var(--accent-red);
        }

        .badge {
            border-radius: 6px;
            font-size: 0.75rem;
            padding: 0.4rem 0.8rem;
        }

        .table-dark {
            background: var(--bg-tertiary);
            border-color: var(--border-color);
        }

        .table-dark td, .table-dark th {
            border-color: var(--border-color);
        }

        .pagination .page-link {
            background: var(--bg-tertiary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        .pagination .page-link:hover {
            background: var(--hover-bg);
            border-color: var(--accent-blue);
            color: var(--accent-blue);
        }

        .search-container {
            position: relative;
        }

        .search-container .form-control {
            padding-left: 2.5rem;
        }

        .search-container .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            z-index: 10;
        }

        .floating-action-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--accent-blue), #4493f8);
            border: none;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 8px 25px rgba(88, 166, 255, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .floating-action-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(88, 166, 255, 0.6);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        /* Fix dropdown z-index and positioning */
        .dropdown-menu {
            z-index: 9999 !important;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
        }

        .card .dropdown-menu {
            z-index: 9999 !important;
        }

        .dropdown-menu.show {
            z-index: 9999 !important;
        }

        /* Ensure dropdown appears in correct position */
        .card .dropdown {
            position: relative;
        }

        .card .dropdown-menu-end {
            right: 0 !important;
            left: auto !important;
        }

        /* Ensure proper overflow handling */
        .card-body {
            overflow: visible;
        }

        .card {
            overflow: visible;
        }

        .row {
            overflow: visible;
        }

        .col-lg-4, .col-md-6 {
            overflow: visible;
        }

        /* Timeline Styles */
        .timeline-container {
            position: relative;
        }

        .timeline-header {
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 1rem;
        }

        .timeline-legend {
            display: flex;
            gap: 1rem;
            font-size: 0.875rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .legend-color.today {
            background: var(--accent-blue);
        }

        .legend-color.weekend {
            background: var(--accent-purple);
        }

        .legend-color.past {
            background: var(--text-secondary);
        }

        .timeline-scroll {
            overflow-x: auto;
            padding-bottom: 1rem;
        }

        .timeline {
            display: flex;
            gap: 1rem;
            min-width: max-content;
            padding: 1rem 0;
        }

        .timeline-day {
            min-width: 200px;
            background: var(--bg-tertiary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .timeline-day.today {
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 2px rgba(88, 166, 255, 0.2);
        }

        .timeline-day.weekend {
            background: rgba(165, 165, 255, 0.1);
        }

        .timeline-day.past {
            opacity: 0.7;
        }

        .timeline-date {
            text-align: center;
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            background: rgba(45, 55, 72, 0.7);
            border-radius: 8px 8px 0 0;
        }

        .date-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--text-primary);
        }

        .date-month {
            font-size: 0.875rem;
            color: var(--text-secondary);
            text-transform: uppercase;
        }

        .date-weekday {
            font-size: 0.75rem;
            color: var(--text-secondary);
        }

        .timeline-content {
            padding: 1rem;
            min-height: 100px;
        }

        .timeline-task {
            background: rgba(74, 85, 104, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 0.75rem;
            margin-bottom: 0.75rem;
            transition: all 0.3s ease;
        }

        .timeline-task:hover {
            border-color: var(--accent-blue);
            box-shadow: 0 4px 12px rgba(88, 166, 255, 0.2);
        }

        .timeline-task:last-child {
            margin-bottom: 0;
        }

        .task-time {
            font-size: 0.75rem;
            color: var(--accent-blue);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .task-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .task-title a {
            color: var(--text-primary);
        }

        .task-title a:hover {
            color: var(--accent-blue);
        }

        .task-meta {
            display: flex;
            gap: 0.25rem;
            flex-wrap: wrap;
        }

        .badge-sm {
            font-size: 0.65rem;
            padding: 0.25rem 0.5rem;
        }

        .no-tasks {
            text-align: center;
            padding: 2rem 0;
        }

        .card-sm {
            background: rgba(74, 85, 104, 0.7);
            border: 1px solid var(--border-color);
        }

        .card-sm:hover {
            border-color: var(--accent-blue);
            box-shadow: 0 4px 12px rgba(88, 166, 255, 0.2);
        }

        /* Monthly Calendar Styles */
        .monthly-container {
            position: relative;
        }

        .month-calendar {
            background: var(--bg-tertiary);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
        }

        .month-header {
            display: flex;
            justify-content: between;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 1rem;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: var(--border-color);
            border-radius: 8px;
            overflow: hidden;
        }

        .calendar-header {
            display: contents;
        }

        .calendar-day-header {
            background: var(--bg-secondary);
            padding: 0.75rem;
            text-align: center;
            font-weight: 600;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .calendar-week {
            display: contents;
        }

        .calendar-day {
            background: var(--bg-primary);
            min-height: 120px;
            padding: 0.5rem;
            position: relative;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .calendar-day:hover {
            background: var(--hover-bg);
        }

        .calendar-day.today {
            background: rgba(88, 166, 255, 0.1);
            border: 2px solid var(--accent-blue);
        }

        .calendar-day.weekend {
            background: rgba(165, 165, 255, 0.05);
        }

        .calendar-day.past {
            opacity: 0.6;
        }

        .calendar-day.empty {
            background: var(--bg-secondary);
            opacity: 0.3;
        }

        .day-number {
            font-weight: 600;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }

        .task-indicator {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: var(--accent-blue);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .day-tasks {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            max-height: 80px;
            overflow: hidden;
        }

        .mini-task {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.75rem;
            padding: 0.125rem 0;
        }

        .task-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .task-title {
            color: var(--text-primary);
            line-height: 1.2;
        }

        .more-tasks {
            font-size: 0.65rem;
            color: var(--text-secondary);
            font-style: italic;
            margin-top: 0.25rem;
        }

        /* View toggle buttons */
        .btn-group .btn {
            border-radius: 0;
        }

        .btn-group .btn:first-child {
            border-top-left-radius: 6px;
            border-bottom-left-radius: 6px;
        }

        .btn-group .btn:last-child {
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="{% url 'task_list' %}">
                <i class="fas fa-tasks me-2"></i>Task Manager
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                {% if user.is_authenticated %}
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'task_list' %}">
                            <i class="fas fa-list me-1"></i>My Tasks
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'task_timeline' %}">
                            <i class="fas fa-calendar-alt me-1"></i>Timeline
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'category_list' %}">
                            <i class="fas fa-tags me-1"></i>Categories
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'task_create' %}">
                            <i class="fas fa-plus me-1"></i>New Task
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user me-1"></i>{{ user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" style="right: 0; left: auto; margin-right: 0;">
                            <li><a class="dropdown-item" href="{% url 'profile' %}"><i class="fas fa-user-cog me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'logout' %}"><i class="fas fa-sign-out-alt me-2"></i>Sign Out</a></li>
                        </ul>
                    </li>
                </ul>
                {% else %}
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'login' %}">
                            <i class="fas fa-sign-in-alt me-1"></i>Sign In
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'signup' %}">
                            <i class="fas fa-user-plus me-1"></i>Sign Up
                        </a>
                    </li>
                </ul>
                {% endif %}
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show fade-in-up" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

        {% block content %}
        {% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Global dropdown positioning and z-index fix -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Fix dropdown positioning and z-index issues globally
            document.addEventListener('show.bs.dropdown', function (e) {
                const dropdown = e.target.closest('.dropdown');
                const menu = dropdown.querySelector('.dropdown-menu');
                if (menu) {
                    menu.style.zIndex = '99999';
                    menu.style.position = 'absolute';
                    // Force right alignment for navbar dropdowns
                    if (dropdown.closest('.navbar')) {
                        menu.style.right = '0';
                        menu.style.left = 'auto';
                        menu.style.transform = 'none';
                    }
                }
            });

            // Ensure dropdown stays positioned correctly when shown
            document.addEventListener('shown.bs.dropdown', function (e) {
                const dropdown = e.target.closest('.dropdown');
                const menu = dropdown.querySelector('.dropdown-menu');
                if (menu) {
                    menu.style.zIndex = '99999';
                    menu.style.position = 'absolute';
                    // Force right alignment for navbar dropdowns
                    if (dropdown.closest('.navbar')) {
                        menu.style.right = '0';
                        menu.style.left = 'auto';
                        menu.style.transform = 'none';
                    }
                }
            });
        });
    </script>

    {% block extra_js %}
    {% endblock %}
</body>
</html>
