{% extends 'emails/base_email.html' %}

{% block title %}Status Update - {{ task.title }}{% endblock %}

{% block header_subtitle %}📊 Status Update{% endblock %}

{% block content %}
<h2 style="color: #17a2b8; margin-bottom: 20px;">Task Status Changed!</h2>

<p>The status of the following task has been updated:</p>

<div class="task-info" style="border-left-color: #17a2b8;">
    <div class="task-title">{{ task.title }}</div>
    
    {% if task.description %}
    <p style="margin: 10px 0; color: #666;">{{ task.description }}</p>
    {% endif %}
    
    <div class="task-meta">
        <div class="meta-item">
            <span class="meta-label">New Status:</span>
            <span class="meta-value status-{{ task.status }}" style="font-weight: bold; font-size: 16px;">
                {{ task.get_status_display }}
            </span>
        </div>
        <div class="meta-item">
            <span class="meta-label">Priority:</span>
            <span class="meta-value priority-{{ task.priority }}">{{ task.get_priority_display }}</span>
        </div>
        {% if task.due_date %}
        <div class="meta-item">
            <span class="meta-label">Due Date:</span>
            <span class="meta-value">{{ task.due_date|date:"M d, Y \a\t H:i" }}</span>
        </div>
        {% endif %}
        {% if task.category %}
        <div class="meta-item">
            <span class="meta-label">Category:</span>
            <span class="meta-value">{{ task.category.name }}</span>
        </div>
        {% endif %}
    </div>
    
    {% if task.has_steps %}
    <div style="margin-top: 20px;">
        <h4 style="margin-bottom: 10px;">Progress: {{ task.progress_percentage }}%</h4>
        <div class="progress-bar">
            <div class="progress-fill" style="width: {{ task.progress_percentage }}%;"></div>
        </div>
        <p style="font-size: 14px; color: #666;">
            {{ task.completed_steps_count }} of {{ task.total_steps_count }} steps completed
        </p>
    </div>
    {% endif %}
</div>

{% if task.status == 'completed' %}
<div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 6px; padding: 15px; margin: 20px 0; color: #155724;">
    <h4 style="margin-top: 0;">🎉 Congratulations!</h4>
    <p style="margin-bottom: 0;">
        This task has been completed successfully. Great job!
    </p>
</div>
{% elif task.status == 'cancelled' %}
<div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 6px; padding: 15px; margin: 20px 0; color: #721c24;">
    <h4 style="margin-top: 0;">❌ Task Cancelled</h4>
    <p style="margin-bottom: 0;">
        This task has been cancelled and will no longer be tracked.
    </p>
</div>
{% elif task.status == 'in_progress' %}
<div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 6px; padding: 15px; margin: 20px 0; color: #0c5460;">
    <h4 style="margin-top: 0;">🚀 In Progress</h4>
    <p style="margin-bottom: 0;">
        This task is now being actively worked on. Keep up the momentum!
    </p>
</div>
{% endif %}

<div style="text-align: center; margin: 30px 0;">
    <a href="http://localhost:8000/{{ task.id }}/" class="btn">View Task Details</a>
</div>
{% endblock %}
